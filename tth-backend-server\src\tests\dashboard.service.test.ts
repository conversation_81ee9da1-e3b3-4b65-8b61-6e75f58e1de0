import { getUserDashboardWidgets } from '../services/dashboard.service';

// Mock the models
jest.mock('../models/User');
jest.mock('../models/UserEmploymentContract');
jest.mock('../models/UserRequest');
jest.mock('../models/Branch');

describe('Dashboard Service', () => {
    describe('getUserDashboardWidgets', () => {
        it('should return 8 widgets including the 4 new ones', async () => {
            // Mock the database calls to return empty arrays to avoid database dependency
            const mockUser = {
                findAll: jest.fn().mockResolvedValue([])
            };
            const mockUserEmploymentContract = {
                findAll: jest.fn().mockResolvedValue([])
            };
            const mockUserRequest = {
                findAll: jest.fn().mockResolvedValue([])
            };
            const mockBranch = {
                findAll: jest.fn().mockResolvedValue([])
            };

            // Replace the imports with mocks
            require('../models/User').User = mockUser;
            require('../models/UserEmploymentContract').UserEmploymentContract = mockUserEmploymentContract;
            require('../models/UserRequest').UserRequest = mockUserRequest;
            require('../models/Branch').Branch = mockBranch;

            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { branchId: 1 };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return 8 widgets (4 original + 4 new)
            expect(widgets).toHaveLength(8);

            // Check that all widgets have required properties
            widgets.forEach(widget => {
                expect(widget).toHaveProperty('id');
                expect(widget).toHaveProperty('title');
                expect(widget).toHaveProperty('type');
                expect(widget).toHaveProperty('data');
                expect(widget).toHaveProperty('order');
            });

            // Check the new widgets specifically
            const newWidgets = widgets.slice(4); // Last 4 widgets are the new ones

            // Widget 5: Onboarding Pipeline Status - Line Chart
            expect(newWidgets[0]).toMatchObject({
                id: 5,
                title: "Onboarding Pipeline Status",
                type: "line_chart",
                order: 5
            });
            expect(newWidgets[0].data).toHaveProperty('chartType', 'line');
            expect(newWidgets[0].data).toHaveProperty('chartData');
            expect(newWidgets[0].data.filters).toHaveProperty('branchFilter', true);

            // Widget 6: Onboarding Pipeline Status - Bar Chart
            expect(newWidgets[1]).toMatchObject({
                id: 6,
                title: "Onboarding Status Overview",
                type: "bar_chart",
                order: 6
            });
            expect(newWidgets[1].data).toHaveProperty('chartType', 'bar');
            expect(newWidgets[1].data).toHaveProperty('chartData');

            // Widget 7: User Contract Graph - Pie Chart
            expect(newWidgets[2]).toMatchObject({
                id: 7,
                title: "User Contract Distribution",
                type: "pie_chart",
                order: 7
            });
            expect(newWidgets[2].data).toHaveProperty('chartType', 'pie');
            expect(newWidgets[2].data).toHaveProperty('chartData');
            expect(newWidgets[2].data.filters).toHaveProperty('branchFilter', true);

            // Widget 8: Leave Comparison Branch wise - Line Chart
            expect(newWidgets[3]).toMatchObject({
                id: 8,
                title: "Leave Comparison by Branch",
                type: "line_chart",
                order: 8
            });
            expect(newWidgets[3].data).toHaveProperty('chartType', 'line');
            expect(newWidgets[3].data).toHaveProperty('chartData');
            expect(newWidgets[3].data.filters).toHaveProperty('branchFilter', true);
        });

        it('should handle errors gracefully', async () => {
            // Mock database error
            const mockUser = {
                findAll: jest.fn().mockRejectedValue(new Error('Database error'))
            };

            require('../models/User').User = mockUser;

            const organizationId = 'test-org-123';
            const userId = 1;

            await expect(getUserDashboardWidgets(organizationId, userId)).rejects.toThrow('Database error');
        });
    });
});
