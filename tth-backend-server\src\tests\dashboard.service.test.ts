// Mock the models before importing the service
const mockUser = {
    findAll: jest.fn().mockResolvedValue([])
};

const mockUserEmploymentContract = {
    findAll: jest.fn().mockResolvedValue([]),
    sequelize: {
        fn: jest.fn(),
        col: jest.fn()
    }
};

const mockUserRequest = {
    findAll: jest.fn().mockResolvedValue([])
};

const mockBranch = {
    findAll: jest.fn().mockResolvedValue([])
};

jest.mock('../models/User', () => ({
    User: mockUser
}));

jest.mock('../models/UserEmployementContract', () => ({
    UserEmploymentContract: mockUserEmploymentContract
}));

jest.mock('../models/UserRequest', () => ({
    UserRequest: mockUserRequest
}));

jest.mock('../models/Branch', () => ({
    Branch: mockBranch
}));

import { getUserDashboardWidgets } from '../services/dashboard.service';

describe('Dashboard Service', () => {
    describe('getUserDashboardWidgets', () => {
        it('should return 8 widgets including the 4 new ones when no filters applied', async () => {

            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { branchId: 1 };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return 8 widgets (4 original + 4 new)
            expect(widgets).toHaveLength(8);

            // Check that all widgets have required properties
            widgets.forEach(widget => {
                expect(widget).toHaveProperty('id');
                expect(widget).toHaveProperty('title');
                expect(widget).toHaveProperty('type');
                expect(widget).toHaveProperty('data');
                expect(widget).toHaveProperty('order');
                expect(widget).toHaveProperty('category');
                expect(['count', 'chart']).toContain(widget.category);

                // Chart widgets should have chartType
                if (widget.category === 'chart') {
                    expect(widget).toHaveProperty('chartType');
                    expect(['line', 'bar', 'pie']).toContain(widget.chartType);
                }
            });

            // Check the new widgets specifically
            const newWidgets = widgets.slice(4); // Last 4 widgets are the new ones

            // Widget 5: Onboarding Pipeline Status - Line Chart
            expect(newWidgets[0]).toMatchObject({
                id: 5,
                title: "Onboarding Pipeline Status",
                type: "line_chart",
                order: 5
            });
            expect(newWidgets[0].data).toHaveProperty('chartType', 'line');
            expect(newWidgets[0].data).toHaveProperty('chartData');
            expect(newWidgets[0].data.filters).toHaveProperty('branchFilter', true);

            // Widget 6: Onboarding Pipeline Status - Bar Chart
            expect(newWidgets[1]).toMatchObject({
                id: 6,
                title: "Onboarding Status Overview",
                type: "bar_chart",
                order: 6
            });
            expect(newWidgets[1].data).toHaveProperty('chartType', 'bar');
            expect(newWidgets[1].data).toHaveProperty('chartData');

            // Widget 7: User Contract Graph - Pie Chart
            expect(newWidgets[2]).toMatchObject({
                id: 7,
                title: "User Contract Distribution",
                type: "pie_chart",
                order: 7
            });
            expect(newWidgets[2].data).toHaveProperty('chartType', 'pie');
            expect(newWidgets[2].data).toHaveProperty('chartData');
            expect(newWidgets[2].data.filters).toHaveProperty('branchFilter', true);

            // Widget 8: Leave Comparison Branch wise - Line Chart
            expect(newWidgets[3]).toMatchObject({
                id: 8,
                title: "Leave Comparison by Branch",
                type: "line_chart",
                order: 8
            });
            expect(newWidgets[3].data).toHaveProperty('chartType', 'line');
            expect(newWidgets[3].data).toHaveProperty('chartData');
            expect(newWidgets[3].data.filters).toHaveProperty('branchFilter', true);
        });

        it('should return only count widgets when widgetType is "counts"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'counts' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only count widgets (first 4 widgets)
            expect(widgets).toHaveLength(4);
            widgets.forEach(widget => {
                expect(widget.category).toBe('count');
            });
        });

        it('should return only chart widgets when widgetType is "charts"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only chart widgets (last 4 widgets)
            expect(widgets).toHaveLength(4);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget).toHaveProperty('chartType');
            });
        });

        it('should return only line chart widgets when widgetType is "charts" and chartType is "line"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const, chartType: 'line' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only line chart widgets (widgets 5 and 8)
            expect(widgets).toHaveLength(2);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget.chartType).toBe('line');
            });
        });

        it('should return only bar chart widgets when widgetType is "charts" and chartType is "bar"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const, chartType: 'bar' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only bar chart widgets (widget 6)
            expect(widgets).toHaveLength(1);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget.chartType).toBe('bar');
            });
        });

        it('should return only pie chart widgets when widgetType is "charts" and chartType is "pie"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const, chartType: 'pie' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only pie chart widgets (widget 7)
            expect(widgets).toHaveLength(1);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget.chartType).toBe('pie');
            });
        });

        it('should return all widgets when widgetType is "all"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'all' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return all 8 widgets
            expect(widgets).toHaveLength(8);
        });

        it('should handle errors gracefully', async () => {
            // Mock database error for one of the helper functions
            mockUser.findAll.mockRejectedValueOnce(new Error('Database error'));

            const organizationId = 'test-org-123';
            const userId = 1;

            // The function should still return widgets even if some data calls fail
            // because it has error handling that returns empty chart data on errors
            const widgets = await getUserDashboardWidgets(organizationId, userId);

            // Should still return the basic widgets (first 4) and empty chart data for the rest
            expect(widgets).toHaveLength(8);
            expect(widgets[0].category).toBe('count'); // Basic widgets should still work

            // Reset the mock for other tests
            mockUser.findAll.mockResolvedValue([]);
        });
    });
});
