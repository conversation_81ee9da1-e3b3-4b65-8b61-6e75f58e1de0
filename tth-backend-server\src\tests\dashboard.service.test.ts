// Mock the models before importing the service
const mockUser = {
    findAll: jest.fn().mockImplementation((options) => {
        // Mock different responses based on query
        if (options?.attributes?.includes('user_status')) {
            // For status count queries
            return Promise.resolve([
                { user_status: 'COMPLETED', count: '10' },
                { user_status: 'PENDING', count: '5' },
                { user_status: 'ONGOING', count: '3' }
            ]);
        } else {
            // For regular user queries
            return Promise.resolve([
                { id: 1, createdAt: '2024-01-15T10:00:00Z' },
                { id: 2, createdAt: '2024-02-15T10:00:00Z' },
                { id: 3, createdAt: '2024-03-15T10:00:00Z' }
            ]);
        }
    }),
    sequelize: {
        fn: jest.fn().mockReturnValue('COUNT(*)'),
        col: jest.fn().mockReturnValue('id')
    }
};

const mockUserEmploymentContract = {
    findAll: jest.fn().mockResolvedValue([
        { contract_status: 'ACTIVE', count: '10' },
        { contract_status: 'INACTIVE', count: '5' },
        { contract_status: 'DELETED', count: '2' }
    ]),
    sequelize: {
        fn: jest.fn().mockReturnValue('COUNT(*)'),
        col: jest.fn().mockReturnValue('UserEmploymentContract.id')
    }
};

const mockUserRequest = {
    findAll: jest.fn().mockResolvedValue([
        { leave_days: 2, createdAt: '2024-01-15T10:00:00Z', 'request_from_users.branch_id': 1 },
        { leave_days: 3, createdAt: '2024-02-15T10:00:00Z', 'request_from_users.branch_id': 1 },
        { leave_days: 1, createdAt: '2024-03-15T10:00:00Z', 'request_from_users.branch_id': 2 }
    ])
};

const mockBranch = {
    findAll: jest.fn().mockResolvedValue([
        { id: 1, branch_name: 'Engineering' },
        { id: 2, branch_name: 'Marketing' }
    ])
};

jest.mock('../models/User', () => ({
    User: mockUser,
    user_status: {
        COMPLETED: 'COMPLETED',
        PENDING: 'PENDING',
        ONGOING: 'ONGOING',
        VERIFIED: 'VERIFIED'
    }
}));

jest.mock('../models/UserEmployementContract', () => ({
    UserEmploymentContract: mockUserEmploymentContract,
    contract_status: {
        ACTIVE: 'ACTIVE',
        INACTIVE: 'INACTIVE',
        DELETED: 'DELETED'
    }
}));

jest.mock('../models/UserRequest', () => ({
    UserRequest: mockUserRequest,
    request_status: {
        APPROVED: 'APPROVED',
        PENDING: 'PENDING',
        REJECTED: 'REJECTED'
    }
}));

jest.mock('../models/Branch', () => ({
    Branch: mockBranch
}));

jest.mock('sequelize', () => ({
    Op: {
        gte: Symbol('gte'),
        lte: Symbol('lte'),
        between: Symbol('between')
    }
}));

import { getUserDashboardWidgets } from '../services/dashboard.service';

describe('Dashboard Service', () => {
    describe('getUserDashboardWidgets', () => {
        it('should return 8 widgets including the 4 new ones when no filters applied', async () => {

            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { branchId: 1 };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return 8 widgets (4 original + 4 new)
            expect(widgets).toHaveLength(8);

            // Check that all widgets have required properties
            widgets.forEach(widget => {
                expect(widget).toHaveProperty('id');
                expect(widget).toHaveProperty('title');
                expect(widget).toHaveProperty('type');
                expect(widget).toHaveProperty('data');
                expect(widget).toHaveProperty('order');
                expect(widget).toHaveProperty('category');
                expect(['count', 'chart']).toContain(widget.category);

                // Chart widgets should have chartType
                if (widget.category === 'chart') {
                    expect(widget).toHaveProperty('chartType');
                    expect(['line', 'bar', 'pie']).toContain(widget.chartType);
                }
            });

            // Check the new widgets specifically
            const newWidgets = widgets.slice(4); // Last 4 widgets are the new ones

            // Widget 5: Onboarding Pipeline Status - Line Chart
            expect(newWidgets[0]).toMatchObject({
                id: 5,
                type: "line_chart",
                order: 5
            });
            expect(newWidgets[0].data).toHaveProperty('chartType', 'line');
            expect(newWidgets[0].data).toHaveProperty('chartData');
            expect(newWidgets[0].data.chartData).toHaveProperty('success', true);
            expect(newWidgets[0].data.chartData).toHaveProperty('data');
            expect(newWidgets[0].data.filters).toHaveProperty('branchFilter', true);

            // Widget 6: Onboarding Pipeline Status - Bar Chart
            expect(newWidgets[1]).toMatchObject({
                id: 6,
                type: "bar_chart",
                order: 6
            });
            expect(newWidgets[1].data).toHaveProperty('chartType', 'bar');
            expect(newWidgets[1].data).toHaveProperty('chartData');
            expect(newWidgets[1].data.chartData).toHaveProperty('success', true);
            expect(newWidgets[1].data.chartData).toHaveProperty('data');

            // Widget 7: User Contract Graph - Pie Chart
            expect(newWidgets[2]).toMatchObject({
                id: 7,
                type: "pie_chart",
                order: 7
            });
            expect(newWidgets[2].data).toHaveProperty('chartType', 'pie');
            expect(newWidgets[2].data).toHaveProperty('chartData');
            expect(newWidgets[2].data.chartData).toHaveProperty('success', true);
            expect(newWidgets[2].data.chartData).toHaveProperty('data');
            expect(newWidgets[2].data.filters).toHaveProperty('branchFilter', true);

            // Widget 8: Leave Comparison Branch wise - Line Chart
            expect(newWidgets[3]).toMatchObject({
                id: 8,
                type: "line_chart",
                order: 8
            });
            expect(newWidgets[3].data).toHaveProperty('chartType', 'line');
            expect(newWidgets[3].data).toHaveProperty('chartData');
            expect(newWidgets[3].data.chartData).toHaveProperty('success', true);
            expect(newWidgets[3].data.chartData).toHaveProperty('data');
            expect(newWidgets[3].data.filters).toHaveProperty('branchFilter', true);
        });

        it('should return only count widgets when widgetType is "counts"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'counts' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only count widgets (first 4 widgets)
            expect(widgets).toHaveLength(4);
            widgets.forEach(widget => {
                expect(widget.category).toBe('count');
            });
        });

        it('should return only chart widgets when widgetType is "charts"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only chart widgets (last 4 widgets)
            expect(widgets).toHaveLength(4);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget).toHaveProperty('chartType');
            });
        });

        it('should return only line chart widgets when widgetType is "charts" and chartType is "line"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const, chartType: 'line' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only line chart widgets (widgets 5 and 8)
            expect(widgets).toHaveLength(2);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget.chartType).toBe('line');
            });
        });

        it('should return only bar chart widgets when widgetType is "charts" and chartType is "bar"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const, chartType: 'bar' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only bar chart widgets (widget 6)
            expect(widgets).toHaveLength(1);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget.chartType).toBe('bar');
            });
        });

        it('should return only pie chart widgets when widgetType is "charts" and chartType is "pie"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'charts' as const, chartType: 'pie' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return only pie chart widgets (widget 7)
            expect(widgets).toHaveLength(1);
            widgets.forEach(widget => {
                expect(widget.category).toBe('chart');
                expect(widget.chartType).toBe('pie');
            });
        });

        it('should return all widgets when widgetType is "all"', async () => {
            const organizationId = 'test-org-123';
            const userId = 1;
            const filters = { widgetType: 'all' as const };

            const widgets = await getUserDashboardWidgets(organizationId, userId, filters);

            // Should return all 8 widgets
            expect(widgets).toHaveLength(8);
        });

        it('should handle errors gracefully', async () => {
            // Mock database error for one of the helper functions
            mockUser.findAll.mockRejectedValueOnce(new Error('Database error'));

            const organizationId = 'test-org-123';
            const userId = 1;

            // The function should still return widgets even if some data calls fail
            // because it has error handling that returns empty chart data on errors
            const widgets = await getUserDashboardWidgets(organizationId, userId);

            // Should still return the basic widgets (first 4) and empty chart data for the rest
            expect(widgets).toHaveLength(8);
            expect(widgets[0].category).toBe('count'); // Basic widgets should still work

            // Reset the mock for other tests
            mockUser.findAll.mockResolvedValue([]);
        });
    });
});
