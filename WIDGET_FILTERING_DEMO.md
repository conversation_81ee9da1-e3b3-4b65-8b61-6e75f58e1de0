# Dashboard Widget Filtering Implementation

## Overview
Successfully implemented widget filtering functionality in the `getUserDashboardWidgets` function with support for filtering by widget type and chart type.

## Updated Function Signature
```typescript
export const getUserDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: {
        branchId?: number;
        widgetType?: "counts" | "charts" | "all";
        chartType?: "line" | "bar" | "pie";
    }
): Promise<DashboardWidget[]>
```

## Widget Categories

### Count Widgets (category: "count")
1. **My Tasks** (ID: 1) - Task summary with pending, completed, overdue counts
2. **My Leave Balance** (ID: 2) - Leave balance with available, used, pending counts
3. **My Recent Activities** (ID: 3) - Activity feed with recent actions
4. **My Performance** (ID: 4) - Performance metrics with trend data

### Chart Widgets (category: "chart")
1. **Onboarding Pipeline Status** (ID: 5) - Line chart showing onboarding over time
2. **Onboarding Status Overview** (ID: 6) - Bar chart comparing onboarding statuses
3. **User Contract Distribution** (ID: 7) - Pie chart showing contract status distribution
4. **Leave Comparison by Branch** (ID: 8) - Line chart comparing leave data across branches

## Filtering Examples

### 1. Get All Widgets (Default Behavior)
```typescript
const allWidgets = await getUserDashboardWidgets(orgId, userId);
// Returns: 8 widgets (4 count + 4 chart)

const allWidgetsExplicit = await getUserDashboardWidgets(orgId, userId, { widgetType: "all" });
// Returns: 8 widgets (4 count + 4 chart)
```

### 2. Get Only Count Widgets
```typescript
const countWidgets = await getUserDashboardWidgets(orgId, userId, { 
    widgetType: "counts" 
});
// Returns: 4 widgets (My Tasks, My Leave Balance, My Recent Activities, My Performance)
```

### 3. Get Only Chart Widgets
```typescript
const chartWidgets = await getUserDashboardWidgets(orgId, userId, { 
    widgetType: "charts" 
});
// Returns: 4 widgets (all chart widgets regardless of chart type)
```

### 4. Get Only Line Chart Widgets
```typescript
const lineCharts = await getUserDashboardWidgets(orgId, userId, { 
    widgetType: "charts",
    chartType: "line"
});
// Returns: 2 widgets (Onboarding Pipeline Status, Leave Comparison by Branch)
```

### 5. Get Only Bar Chart Widgets
```typescript
const barCharts = await getUserDashboardWidgets(orgId, userId, { 
    widgetType: "charts",
    chartType: "bar"
});
// Returns: 1 widget (Onboarding Status Overview)
```

### 6. Get Only Pie Chart Widgets
```typescript
const pieCharts = await getUserDashboardWidgets(orgId, userId, { 
    widgetType: "charts",
    chartType: "pie"
});
// Returns: 1 widget (User Contract Distribution)
```

### 7. Combine with Branch Filtering
```typescript
const branchLineCharts = await getUserDashboardWidgets(orgId, userId, { 
    branchId: 123,
    widgetType: "charts",
    chartType: "line"
});
// Returns: 2 line chart widgets filtered by branch (where applicable)
```

## Updated Widget Structure

Each widget now includes:
```typescript
{
    id: number;
    title: string;
    type: string;
    data: any;
    order: number;
    category: "count" | "chart";        // NEW: Widget category
    chartType?: "line" | "bar" | "pie"; // NEW: Chart type (only for chart widgets)
}
```

## Implementation Details

### Filtering Logic
1. **No filters**: Returns all widgets (backward compatible)
2. **widgetType: "counts"**: Filters widgets where `category === "count"`
3. **widgetType: "charts"**: Filters widgets where `category === "chart"`
4. **widgetType: "charts" + chartType**: Further filters chart widgets by specific chart type
5. **widgetType: "all"**: Returns all widgets (explicit all)

### Backward Compatibility
- Existing API calls without filters continue to work unchanged
- All widgets now have the required `category` field
- Chart widgets have the `chartType` field for enhanced filtering

### Error Handling
- Invalid filter values are ignored (graceful degradation)
- Database errors in helper functions return empty chart data
- Widget filtering happens after data retrieval to ensure consistency

## Testing
Comprehensive unit tests cover:
- Default behavior (all widgets)
- Count widget filtering
- Chart widget filtering
- Specific chart type filtering (line, bar, pie)
- Combined filtering scenarios
- Widget structure validation

## Performance Considerations
- Filtering is applied in-memory after data retrieval
- Chart data is only fetched when needed
- Branch filtering is applied at the database level for efficiency
- Widget filtering adds minimal overhead to response time

## Future Enhancements
- Add more chart types (area, scatter, etc.)
- Implement widget ordering/sorting options
- Add date range filtering for time-based widgets
- Support for custom widget categories
- Widget favoriting/pinning functionality
