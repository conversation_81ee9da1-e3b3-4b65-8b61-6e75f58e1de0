import { User, user_status } from "../models/User";
import { UserEmploymentContract, contract_status } from "../models/UserEmployementContract";
import { UserRequest, request_status } from "../models/UserRequest";
import { Branch } from "../models/Branch";
import { Op } from "sequelize";
import moment from "moment";

// Configure moment.js for consistent behavior
moment.locale('en'); // Set default locale

/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets
 */

// Types for Recharts compatibility
interface RechartsDataPoint {
    name: string;
    value: number;
    [key: string]: any; // Allow additional properties for multi-series data
}

interface RechartsLineBarData {
    data: RechartsDataPoint[];
    xAxisKey: string;
    yAxisKey: string;
    series: {
        dataKey: string;
        name: string;
        color: string;
        type?: 'line' | 'bar';
    }[];
}

interface RechartsPieData {
    data: RechartsDataPoint[];
    nameKey: string;
    valueKey: string;
    colors: string[];
}

interface CommonChartResponse {
    success: boolean;
    chartType: 'line' | 'bar' | 'pie';
    title: string;
    data: RechartsLineBarData | RechartsPieData;
    metadata?: {
        totalRecords: number;
        dateRange?: string;
        filters?: any;
        lastUpdated: string;
    };
}

interface FilterOptions {
    branchId?: number;
    widgetType?: "counts" | "charts" | "all";
    chartType?: "line" | "bar" | "pie";
}

// Cache for frequently accessed data (in production, use Redis or similar)
const dataCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data or fetch if expired
 */
const getCachedData = async <T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl: number = CACHE_TTL
): Promise<T> => {
    const cached = dataCache.get(key);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < ttl) {
        return cached.data as T;
    }

    const data = await fetchFn();
    dataCache.set(key, { data, timestamp: now });
    return data;
};

/**
 * Validate required parameters
 */
const validateParameters = (organizationId: string, userId: number): void => {
    if (!organizationId || typeof organizationId !== 'string') {
        throw new Error('Invalid organizationId: must be a non-empty string');
    }
    if (!userId || typeof userId !== 'number' || userId <= 0) {
        throw new Error('Invalid userId: must be a positive number');
    }
};

/**
 * Generate date range for the last N months using Moment.js
 */
const generateMonthRange = (months: number): { start: Date; monthKeys: string[]; monthLabels: string[] } => {
    const start = moment().subtract(months, 'months').startOf('month').toDate();

    const monthKeys: string[] = [];
    const monthLabels: string[] = [];

    for (let i = months - 1; i >= 0; i--) {
        const monthMoment = moment().subtract(i, 'months');
        monthKeys.push(monthMoment.format('YYYY-MM'));
        monthLabels.push(monthMoment.format('MMM YYYY'));
    }

    return { start, monthKeys, monthLabels };
};

/**
 * Utility functions for consistent date handling with Moment.js
 */
const DateUtils = {
    /**
     * Validate and format date to YYYY-MM format for month grouping
     */
    toMonthKey: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to toMonthKey: ${date}`);
            return moment().format('YYYY-MM'); // Return current month as fallback
        }
        return momentDate.format('YYYY-MM');
    },

    /**
     * Format date to human-readable month label
     */
    toMonthLabel: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to toMonthLabel: ${date}`);
            return moment().format('MMM YYYY'); // Return current month as fallback
        }
        return momentDate.format('MMM YYYY');
    },

    /**
     * Check if date is within the last N months
     */
    isWithinLastMonths: (date: string | Date | moment.Moment, months: number): boolean => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to isWithinLastMonths: ${date}`);
            return false;
        }
        const cutoffDate = moment().subtract(months, 'months').startOf('month');
        return momentDate.isAfter(cutoffDate);
    },

    /**
     * Get start of month for a given date
     */
    startOfMonth: (date: string | Date | moment.Moment): Date => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to startOfMonth: ${date}`);
            return moment().startOf('month').toDate(); // Return current month start as fallback
        }
        return momentDate.startOf('month').toDate();
    },

    /**
     * Format date for display with validation
     */
    formatForDisplay: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to formatForDisplay: ${date}`);
            return moment().format('YYYY-MM-DD'); // Return current date as fallback
        }
        return momentDate.format('YYYY-MM-DD');
    },

    /**
     * Get relative time from now (e.g., "2 days ago")
     */
    fromNow: (date: string | Date | moment.Moment): string => {
        const momentDate = moment(date);
        if (!momentDate.isValid()) {
            console.warn(`Invalid date provided to fromNow: ${date}`);
            return 'Invalid date';
        }
        return momentDate.fromNow();
    },

    /**
     * Check if date is valid
     */
    isValid: (date: string | Date | moment.Moment): boolean => {
        return moment(date).isValid();
    },

    /**
     * Get current timestamp in ISO format
     */
    now: (): string => {
        return moment().toISOString();
    }
};

/**
 * Get onboarding pipeline status data for line chart (Recharts format)
 * Optimized with caching and better date filtering
 */
const getOnboardingPipelineLineData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `onboarding_line_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            const { start: sixMonthsAgo, monthKeys, monthLabels } = generateMonthRange(6);

            // Build where clause with proper date filtering
            const whereClause: any = {
                organization_id: organizationId,
                user_status: user_status.COMPLETED,
                createdAt: {
                    [Op.gte]: sixMonthsAgo
                }
            };

            if (filters?.branchId) {
                whereClause.branch_id = filters.branchId;
            }

            // Optimized query with date filtering at database level
            const completedUsers = await User.findAll({
                where: whereClause,
                attributes: ['id', 'createdAt'],
                raw: true, // Better performance for aggregation
                order: [['createdAt', 'ASC']]
            });

            // Create a map for O(1) lookup instead of filtering arrays
            const userCountByMonth = new Map<string, number>();
            monthKeys.forEach(key => userCountByMonth.set(key, 0));

            // Single pass through users to count by month using Moment.js
            completedUsers.forEach((user: any) => {
                if (user.createdAt) {
                    const monthKey = DateUtils.toMonthKey(user.createdAt);
                    if (userCountByMonth.has(monthKey)) {
                        userCountByMonth.set(monthKey, userCountByMonth.get(monthKey)! + 1);
                    }
                }
            });

            // Format data for Recharts line chart
            const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => ({
                name: label,
                value: userCountByMonth.get(monthKeys[index]) || 0,
                month: label,
                completed: userCountByMonth.get(monthKeys[index]) || 0
            }));

            return {
                success: true,
                chartType: 'line',
                title: 'Onboarding Pipeline Status',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: [{
                        dataKey: 'completed',
                        name: 'Completed Onboarding',
                        color: '#4F46E5',
                        type: 'line'
                    }]
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: completedUsers.length,
                    dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getOnboardingPipelineLineData:', error);
            // Return empty structure instead of throwing to prevent cascade failures
            return {
                success: false,
                chartType: 'line',
                title: 'Onboarding Pipeline Status',
                data: {
                    data: [],
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: []
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

/**
 * Get onboarding pipeline status data for bar chart (Recharts format)
 * Optimized with caching and better error handling
 */
const getOnboardingPipelineBarData = async (organizationId: string): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `onboarding_bar_${organizationId}`;

    return getCachedData(cacheKey, async () => {
        try {
            const statusCounts = await User.findAll({
                where: {
                    organization_id: organizationId
                },
                attributes: [
                    'user_status',
                    [User.sequelize!.fn('COUNT', User.sequelize!.col('id')), 'count']
                ],
                group: ['user_status'],
                raw: true
            });

            const statusMap = {
                [user_status.COMPLETED]: 'Completed',
                [user_status.PENDING]: 'Pending',
                [user_status.ONGOING]: 'Ongoing',
                [user_status.VERIFIED]: 'Verified'
            };

            const statusColors = {
                'Completed': '#10B981',
                'Pending': '#F59E0B',
                'Ongoing': '#3B82F6',
                'Verified': '#8B5CF6'
            };

            // Format data for Recharts bar chart
            const chartData: RechartsDataPoint[] = statusCounts
                .filter((item: any) => statusMap[item.user_status as keyof typeof statusMap])
                .map((item: any) => {
                    const statusName = statusMap[item.user_status as keyof typeof statusMap];
                    return {
                        name: statusName,
                        value: parseInt(item.count, 10) || 0,
                        status: statusName,
                        count: parseInt(item.count, 10) || 0,
                        color: statusColors[statusName as keyof typeof statusColors]
                    };
                });

            const totalRecords = chartData.reduce((sum, item) => sum + item.value, 0);

            return {
                success: true,
                chartType: 'bar',
                title: 'Onboarding Status Overview',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: [{
                        dataKey: 'count',
                        name: 'User Count',
                        color: '#4F46E5',
                        type: 'bar'
                    }]
                } as RechartsLineBarData,
                metadata: {
                    totalRecords,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getOnboardingPipelineBarData:', error);
            return {
                success: false,
                chartType: 'bar',
                title: 'Onboarding Status Overview',
                data: {
                    data: [],
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: []
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

/**
 * Get user contract status data for pie chart (Recharts format)
 * Optimized with caching and better query structure
 */
const getUserContractPieData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `contract_pie_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            // Build where clause for User table
            const userWhere: any = {
                organization_id: organizationId
            };

            if (filters?.branchId) {
                userWhere.branch_id = filters.branchId;
            }

            const contractCounts = await UserEmploymentContract.findAll({
                include: [{
                    model: User,
                    as: 'user_employment_contract',
                    where: userWhere,
                    attributes: [] // Don't select user attributes for better performance
                }],
                attributes: [
                    'contract_status',
                    [UserEmploymentContract.sequelize!.fn('COUNT', UserEmploymentContract.sequelize!.col('UserEmploymentContract.id')), 'count']
                ],
                group: ['contract_status'],
                raw: true
            });

            const statusMap = {
                [contract_status.ACTIVE]: 'Active',
                [contract_status.INACTIVE]: 'Inactive',
                [contract_status.DELETED]: 'Terminated'
            };

            const statusColors = ['#10B981', '#F59E0B', '#EF4444'];

            // Format data for Recharts pie chart
            const chartData: RechartsDataPoint[] = contractCounts
                .filter((item: any) => statusMap[item.contract_status as keyof typeof statusMap])
                .map((item: any, index: number) => {
                    const statusName = statusMap[item.contract_status as keyof typeof statusMap];
                    return {
                        name: statusName,
                        value: parseInt(item.count, 10) || 0,
                        status: statusName,
                        count: parseInt(item.count, 10) || 0,
                        color: statusColors[index % statusColors.length]
                    };
                });

            const totalRecords = chartData.reduce((sum, item) => sum + item.value, 0);

            return {
                success: true,
                chartType: 'pie',
                title: 'User Contract Distribution',
                data: {
                    data: chartData,
                    nameKey: 'name',
                    valueKey: 'value',
                    colors: statusColors
                } as RechartsPieData,
                metadata: {
                    totalRecords,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getUserContractPieData:', error);
            return {
                success: false,
                chartType: 'pie',
                title: 'User Contract Distribution',
                data: {
                    data: [],
                    nameKey: 'name',
                    valueKey: 'value',
                    colors: []
                } as RechartsPieData,
                metadata: {
                    totalRecords: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

/**
 * Get leave comparison data by branch for line chart (Recharts format)
 * Heavily optimized with parallel queries and efficient data processing
 */
const getLeaveComparisonByBranchData = async (organizationId: string, filters?: FilterOptions): Promise<CommonChartResponse> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `leave_comparison_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            const { monthKeys, monthLabels } = generateMonthRange(6);

            // Build where clauses
            const branchWhere: any = {
                organization_id: organizationId
            };

            const userWhere: any = {
                organization_id: organizationId
            };

            if (filters?.branchId) {
                branchWhere.id = filters.branchId;
                userWhere.branch_id = filters.branchId;
            }

            // Execute queries in parallel for better performance
            const [branches, leaveData] = await Promise.all([
                Branch.findAll({
                    where: branchWhere,
                    attributes: ['id', 'branch_name'],
                    raw: true
                }),
                UserRequest.findAll({
                    where: {
                        request_type: 'casual',
                        request_status: request_status.APPROVED
                    },
                    include: [{
                        model: User,
                        as: 'request_from_users',
                        where: userWhere,
                        attributes: ['branch_id'],
                        required: true
                    }],
                    attributes: ['leave_days', 'createdAt'],
                    raw: true
                })
            ]);

            if (branches.length === 0) {
                return {
                    success: true,
                    chartType: 'line',
                    title: 'Leave Comparison by Branch',
                    data: {
                        data: [],
                        xAxisKey: 'name',
                        yAxisKey: 'value',
                        series: []
                    } as RechartsLineBarData,
                    metadata: {
                        totalRecords: 0,
                        lastUpdated: DateUtils.now()
                    }
                };
            }

            // Create efficient lookup structures
            const leaveByBranchAndMonth = new Map<string, Map<string, number>>();

            // Initialize data structure
            branches.forEach((branch: any) => {
                const monthMap = new Map<string, number>();
                monthKeys.forEach(key => monthMap.set(key, 0));
                leaveByBranchAndMonth.set(branch.id.toString(), monthMap);
            });

            // Single pass through leave data to aggregate using Moment.js
            leaveData.forEach((leave: any) => {
                if (leave.createdAt && leave['request_from_users.branch_id']) {
                    // Filter by date range using DateUtils
                    if (DateUtils.isWithinLastMonths(leave.createdAt, 6)) {
                        const monthKey = DateUtils.toMonthKey(leave.createdAt);
                        const branchId = leave['request_from_users.branch_id'].toString();
                        const branchData = leaveByBranchAndMonth.get(branchId);

                        if (branchData && branchData.has(monthKey)) {
                            const currentTotal = branchData.get(monthKey) || 0;
                            branchData.set(monthKey, currentTotal + (leave.leave_days || 0));
                        }
                    }
                }
            });

            // Format data for Recharts multi-line chart
            const chartData: RechartsDataPoint[] = monthLabels.map((label, index) => {
                const dataPoint: RechartsDataPoint = {
                    name: label,
                    value: 0, // Will be calculated as sum of all branches
                    month: label
                };

                // Add each branch's data as a separate property
                branches.forEach((branch: any) => {
                    const branchData = leaveByBranchAndMonth.get(branch.id.toString());
                    const branchValue = branchData?.get(monthKeys[index]) || 0;
                    dataPoint[`branch_${branch.id}`] = branchValue;
                    dataPoint[branch.branch_name] = branchValue;
                    dataPoint.value += branchValue;
                });

                return dataPoint;
            });

            // Generate series for each branch
            const colors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];
            const series = branches.map((branch: any, index: number) => ({
                dataKey: branch.branch_name,
                name: branch.branch_name,
                color: colors[index % colors.length],
                type: 'line' as const
            }));

            const totalRecords = leaveData.length;

            return {
                success: true,
                chartType: 'line',
                title: 'Leave Comparison by Branch',
                data: {
                    data: chartData,
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series
                } as RechartsLineBarData,
                metadata: {
                    totalRecords,
                    dateRange: `${monthLabels[0]} - ${monthLabels[monthLabels.length - 1]}`,
                    filters: filters,
                    lastUpdated: DateUtils.now()
                }
            };
        } catch (error) {
            console.error('Error in getLeaveComparisonByBranchData:', error);
            return {
                success: false,
                chartType: 'line',
                title: 'Leave Comparison by Branch',
                data: {
                    data: [],
                    xAxisKey: 'name',
                    yAxisKey: 'value',
                    series: []
                } as RechartsLineBarData,
                metadata: {
                    totalRecords: 0,
                    lastUpdated: DateUtils.now()
                }
            };
        }
    });
};

export interface DashboardWidget {
    id: number;
    title: string;
    type: string;
    data: any;
    order: number;
    category: "count" | "chart";
    chartType?: "line" | "bar" | "pie";
}

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users
 */
export const getUserDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: FilterOptions
): Promise<DashboardWidget[]> => {
    // Validate input parameters
    validateParameters(organizationId, userId);

    try {
        // Static count widgets (no database calls needed)
        const countWidgets: DashboardWidget[] = [
            {
                id: 1,
                title: "My Tasks",
                type: "task_summary",
                data: {
                    pending: 5,
                    completed: 12,
                    overdue: 2
                },
                order: 1,
                category: "count"
            },
            {
                id: 2,
                title: "My Leave Balance",
                type: "leave_balance",
                data: {
                    available: 15,
                    used: 10,
                    pending: 2
                },
                order: 2,
                category: "count"
            },
            {
                id: 3,
                title: "My Recent Activities",
                type: "activity_feed",
                data: {
                    activities: [
                        { action: "DSR Submitted", date: DateUtils.formatForDisplay(moment().subtract(1, 'day')), status: "completed" },
                        { action: "Leave Request", date: DateUtils.formatForDisplay(moment().subtract(2, 'days')), status: "pending" }
                    ]
                },
                order: 3,
                category: "count"
            },
            {
                id: 4,
                title: "My Performance",
                type: "performance_chart",
                data: {
                    currentMonth: 85,
                    lastMonth: 78,
                    trend: "up"
                },
                order: 4,
                category: "count"
            }
        ];

        // Early return if only count widgets are requested
        if (filters?.widgetType === "counts") {
            return countWidgets;
        }

        // Execute chart data fetching in parallel for better performance
        const chartDataPromises = [
            getOnboardingPipelineLineData(organizationId, filters),
            getOnboardingPipelineBarData(organizationId),
            getUserContractPieData(organizationId, filters),
            getLeaveComparisonByBranchData(organizationId, filters)
        ];

        // Use Promise.allSettled to handle partial failures gracefully
        const chartResults = await Promise.allSettled(chartDataPromises);

        // Create chart widgets with error handling for each
        const chartWidgets: DashboardWidget[] = [];

        // Widget 1: Onboarding Pipeline Status - Line Chart
        if (chartResults[0].status === 'fulfilled' && chartResults[0].value.success) {
            chartWidgets.push({
                id: 5,
                title: chartResults[0].value.title,
                type: "line_chart",
                data: {
                    chartType: "line",
                    chartData: chartResults[0].value, // Full response object
                    filters: { branchFilter: true },
                    description: "Shows onboarding pipeline status over time (completed users only)"
                },
                order: 5,
                category: "chart",
                chartType: "line"
            });
        }

        // Widget 2: Onboarding Pipeline Status - Bar Chart
        if (chartResults[1].status === 'fulfilled' && chartResults[1].value.success) {
            chartWidgets.push({
                id: 6,
                title: chartResults[1].value.title,
                type: "bar_chart",
                data: {
                    chartType: "bar",
                    chartData: chartResults[1].value, // Full response object
                    description: "Compare completed vs pending onboarding status"
                },
                order: 6,
                category: "chart",
                chartType: "bar"
            });
        }

        // Widget 3: User Contract Graph - Pie Chart
        if (chartResults[2].status === 'fulfilled' && chartResults[2].value.success) {
            chartWidgets.push({
                id: 7,
                title: chartResults[2].value.title,
                type: "pie_chart",
                data: {
                    chartType: "pie",
                    chartData: chartResults[2].value, // Full response object
                    filters: { branchFilter: true },
                    description: "Distribution of user contract statuses"
                },
                order: 7,
                category: "chart",
                chartType: "pie"
            });
        }

        // Widget 4: Leave Comparison Branch wise - Line Chart
        if (chartResults[3].status === 'fulfilled' && chartResults[3].value.success) {
            chartWidgets.push({
                id: 8,
                title: chartResults[3].value.title,
                type: "line_chart",
                data: {
                    chartType: "line",
                    chartData: chartResults[3].value, // Full response object
                    filters: { branchFilter: true },
                    description: "Monthly leave data comparison across branches"
                },
                order: 8,
                category: "chart",
                chartType: "line"
            });
        }

        // Log any failed chart data fetches for monitoring
        chartResults.forEach((result, index) => {
            if (result.status === 'rejected') {
                console.error(`Chart data fetch failed for widget ${index + 5}:`, result.reason);
            }
        });

        // Combine widgets based on filter requirements
        let allWidgets: DashboardWidget[] = [];

        if (filters?.widgetType === "charts") {
            allWidgets = chartWidgets;
        } else {
            // Default: return both count and chart widgets
            allWidgets = [...countWidgets, ...chartWidgets];
        }

        // Apply chart type filtering if specified
        if (filters?.widgetType === "charts" && filters?.chartType) {
            allWidgets = allWidgets.filter(widget => widget.chartType === filters.chartType);
        }

        return allWidgets;
    } catch (error) {
        console.error('Error in getUserDashboardWidgets:', error);
        // Return at least the count widgets on error to provide partial functionality
        if (filters?.widgetType !== "charts") {
            return [
                {
                    id: 1,
                    title: "My Tasks",
                    type: "task_summary",
                    data: { pending: 0, completed: 0, overdue: 0 },
                    order: 1,
                    category: "count"
                }
            ];
        }
        throw error;
    }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
export const getDsrDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const _unused = { organizationId, userId, filters };
        const widgets: DashboardWidget[] = [];

        // DSR-specific widgets
        widgets.push({
            id: 1,
            title: "DSR Summary",
            type: "dsr_stats",
            data: {
                totalDSRs: 150,
                pendingDSRs: 25,
                approvedDSRs: 120,
                rejectedDSRs: 5
            },
            order: 1,
            category: "count"
        });

        widgets.push({
            id: 2,
            title: "Recent DSR Activities",
            type: "dsr_activities",
            data: {
                recentSubmissions: 12,
                pendingApprovals: 8,
                todaysSubmissions: 5
            },
            order: 2,
            category: "count"
        });

        widgets.push({
            id: 3,
            title: "DSR Performance",
            type: "dsr_performance",
            data: {
                onTimeSubmissions: 85,
                lateSubmissions: 10,
                averageRating: 4.2
            },
            order: 3,
            category: "count"
        });

        widgets.push({
            id: 4,
            title: "Team DSR Status",
            type: "team_dsr_status",
            data: {
                totalTeamMembers: 15,
                submittedToday: 12,
                pendingToday: 3
            },
            order: 4,
            category: "count"
        });

        return widgets;
    } catch (error) {
        console.error('Error in getAdminDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const _unused = { organizationId, userId, filters };
        const widgets: DashboardWidget[] = [];

        // Setup-specific widgets
        widgets.push({
            id: 1,
            title: "System Configuration",
            type: "system_config",
            data: {
                completedSteps: 8,
                totalSteps: 12,
                progressPercentage: 67
            },
            order: 1,
            category: "count"
        });

        widgets.push({
            id: 2,
            title: "User Setup",
            type: "user_setup",
            data: {
                totalUsers: 25,
                activeUsers: 20,
                pendingInvitations: 5
            },
            order: 2,
            category: "count"
        });

        widgets.push({
            id: 3,
            title: "Module Configuration",
            type: "module_config",
            data: {
                enabledModules: 8,
                totalModules: 12,
                pendingConfiguration: 4
            },
            order: 3,
            category: "count"
        });

        widgets.push({
            id: 4,
            title: "Integration Status",
            type: "integration_status",
            data: {
                connectedServices: 3,
                totalServices: 6,
                failedConnections: 1
            },
            order: 4,
            category: "count"
        });

        return widgets;
    } catch (error) {
        console.error('Error in getSetupDashboardWidgets:', error);
        throw error;
    }
};

export default {
    getUserDashboardWidgets,
    getDsrDashboardWidgets,
    getSetupDashboardWidgets
};
