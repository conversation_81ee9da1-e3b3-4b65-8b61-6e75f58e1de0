import { Dashboard, dashboard_status } from "../models/Dashboard";
import { DashboardModel, model_status } from "../models/DashboardModel";
import { User } from "../models/User";
import { generateDashboard } from "../helper/common";

/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets
 */

export interface DashboardWidget {
    id: number;
    title: string;
    type: string;
    data: any;
    order: number;
}

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users
 */
export const getUserDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // User-specific widgets
        widgets.push({
            id: 1,
            title: "My Tasks",
            type: "task_summary",
            data: {
                pending: 5,
                completed: 12,
                overdue: 2
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "My Leave Balance",
            type: "leave_balance",
            data: {
                available: 15,
                used: 10,
                pending: 2
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "My Recent Activities",
            type: "activity_feed",
            data: {
                activities: [
                    { action: "DSR Submitted", date: "2024-01-15", status: "completed" },
                    { action: "Leave Request", date: "2024-01-14", status: "pending" }
                ]
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "My Performance",
            type: "performance_chart",
            data: {
                currentMonth: 85,
                lastMonth: 78,
                trend: "up"
            },
            order: 4
        });

        return widgets;
    } catch (error) {
        console.error('Error in getUserDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
export const getDsrDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // DSR-specific widgets
        widgets.push({
            id: 1,
            title: "DSR Summary",
            type: "dsr_stats",
            data: {
                totalDSRs: 150,
                pendingDSRs: 25,
                approvedDSRs: 120,
                rejectedDSRs: 5
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "Recent DSR Activities",
            type: "dsr_activities",
            data: {
                recentSubmissions: 12,
                pendingApprovals: 8,
                todaysSubmissions: 5
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "DSR Performance",
            type: "dsr_performance",
            data: {
                onTimeSubmissions: 85,
                lateSubmissions: 10,
                averageRating: 4.2
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "Team DSR Status",
            type: "team_dsr_status",
            data: {
                totalTeamMembers: 15,
                submittedToday: 12,
                pendingToday: 3
            },
            order: 4
        });

        return widgets;
    } catch (error) {
        console.error('Error in getAdminDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // Setup-specific widgets
        widgets.push({
            id: 1,
            title: "System Configuration",
            type: "system_config",
            data: {
                completedSteps: 8,
                totalSteps: 12,
                progressPercentage: 67
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "User Setup",
            type: "user_setup",
            data: {
                totalUsers: 25,
                activeUsers: 20,
                pendingInvitations: 5
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "Module Configuration",
            type: "module_config",
            data: {
                enabledModules: 8,
                totalModules: 12,
                pendingConfiguration: 4
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "Integration Status",
            type: "integration_status",
            data: {
                connectedServices: 3,
                totalServices: 6,
                failedConnections: 1
            },
            order: 4
        });

        return widgets;
    } catch (error) {
        console.error('Error in getSetupDashboardWidgets:', error);
        throw error;
    }
};

export default {
    getUserDashboardWidgets,
    getDsrDashboardWidgets,
    getSetupDashboardWidgets
};
