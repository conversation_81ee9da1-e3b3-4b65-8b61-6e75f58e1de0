import { User, user_status } from "../models/User";
import { UserEmploymentContract, contract_status } from "../models/UserEmployementContract";
import { UserRequest, request_status } from "../models/UserRequest";
import { Branch } from "../models/Branch";
import { Op } from "sequelize";

/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets
 */

// Types for better type safety
interface ChartDataset {
    label?: string;
    data: number[];
    borderColor?: string;
    backgroundColor?: string | string[];
    borderWidth?: number;
    tension?: number;
}

interface ChartData {
    labels: string[];
    datasets: ChartDataset[];
}

interface FilterOptions {
    branchId?: number;
    widgetType?: "counts" | "charts" | "all";
    chartType?: "line" | "bar" | "pie";
}

// Cache for frequently accessed data (in production, use Redis or similar)
const dataCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data or fetch if expired
 */
const getCachedData = async <T>(
    key: string,
    fetchFn: () => Promise<T>,
    ttl: number = CACHE_TTL
): Promise<T> => {
    const cached = dataCache.get(key);
    const now = Date.now();

    if (cached && (now - cached.timestamp) < ttl) {
        return cached.data as T;
    }

    const data = await fetchFn();
    dataCache.set(key, { data, timestamp: now });
    return data;
};

/**
 * Validate required parameters
 */
const validateParameters = (organizationId: string, userId: number): void => {
    if (!organizationId || typeof organizationId !== 'string') {
        throw new Error('Invalid organizationId: must be a non-empty string');
    }
    if (!userId || typeof userId !== 'number' || userId <= 0) {
        throw new Error('Invalid userId: must be a positive number');
    }
};

/**
 * Generate date range for the last N months
 */
const generateMonthRange = (months: number): { start: Date; monthKeys: string[]; monthLabels: string[] } => {
    const start = new Date();
    start.setMonth(start.getMonth() - months);
    start.setDate(1);
    start.setHours(0, 0, 0, 0);

    const monthKeys: string[] = [];
    const monthLabels: string[] = [];

    for (let i = months - 1; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        monthKeys.push(date.toISOString().slice(0, 7));
        monthLabels.push(date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }));
    }

    return { start, monthKeys, monthLabels };
};

/**
 * Get onboarding pipeline status data for line chart
 * Optimized with caching and better date filtering
 */
const getOnboardingPipelineLineData = async (organizationId: string, filters?: FilterOptions): Promise<ChartData> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `onboarding_line_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            const { start: sixMonthsAgo, monthKeys, monthLabels } = generateMonthRange(6);

            // Build where clause with proper date filtering
            const whereClause: any = {
                organization_id: organizationId,
                user_status: user_status.COMPLETED,
                createdAt: {
                    [Op.gte]: sixMonthsAgo
                }
            };

            if (filters?.branchId) {
                whereClause.branch_id = filters.branchId;
            }

            // Optimized query with date filtering at database level
            const completedUsers = await User.findAll({
                where: whereClause,
                attributes: ['id', 'createdAt'],
                raw: true, // Better performance for aggregation
                order: [['createdAt', 'ASC']]
            });

            // Create a map for O(1) lookup instead of filtering arrays
            const userCountByMonth = new Map<string, number>();
            monthKeys.forEach(key => userCountByMonth.set(key, 0));

            // Single pass through users to count by month
            completedUsers.forEach((user: any) => {
                if (user.createdAt) {
                    const monthKey = new Date(user.createdAt).toISOString().slice(0, 7);
                    if (userCountByMonth.has(monthKey)) {
                        userCountByMonth.set(monthKey, userCountByMonth.get(monthKey)! + 1);
                    }
                }
            });

            const monthlyData = monthKeys.map(key => userCountByMonth.get(key) || 0);

            return {
                labels: monthLabels,
                datasets: [{
                    label: 'Completed Onboarding',
                    data: monthlyData,
                    borderColor: '#4F46E5',
                    backgroundColor: 'rgba(79, 70, 229, 0.1)',
                    tension: 0.4
                }]
            };
        } catch (error) {
            console.error('Error in getOnboardingPipelineLineData:', error);
            // Return empty structure instead of throwing to prevent cascade failures
            return {
                labels: [],
                datasets: []
            };
        }
    });
};

/**
 * Get onboarding pipeline status data for bar chart
 * Optimized with caching and better error handling
 */
const getOnboardingPipelineBarData = async (organizationId: string): Promise<ChartData> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `onboarding_bar_${organizationId}`;

    return getCachedData(cacheKey, async () => {
        try {
            const statusCounts = await User.findAll({
                where: {
                    organization_id: organizationId
                },
                attributes: [
                    'user_status',
                    [User.sequelize!.fn('COUNT', User.sequelize!.col('id')), 'count']
                ],
                group: ['user_status'],
                raw: true
            });

            const statusMap = {
                [user_status.COMPLETED]: 'Completed',
                [user_status.PENDING]: 'Pending',
                [user_status.ONGOING]: 'Ongoing',
                [user_status.VERIFIED]: 'Verified'
            };

            const backgroundColors = ['#10B981', '#F59E0B', '#3B82F6', '#8B5CF6'];

            // Pre-allocate arrays and use more efficient processing
            const validStatuses = statusCounts.filter((item: any) =>
                statusMap[item.user_status as keyof typeof statusMap]
            );

            const labels = validStatuses.map((item: any) =>
                statusMap[item.user_status as keyof typeof statusMap]
            );

            const data = validStatuses.map((item: any) =>
                parseInt(item.count, 10) || 0
            );

            return {
                labels,
                datasets: [{
                    label: 'User Count',
                    data,
                    backgroundColor: backgroundColors.slice(0, labels.length),
                    borderWidth: 1
                }]
            };
        } catch (error) {
            console.error('Error in getOnboardingPipelineBarData:', error);
            return {
                labels: [],
                datasets: []
            };
        }
    });
};

/**
 * Get user contract status data for pie chart
 * Optimized with caching and better query structure
 */
const getUserContractPieData = async (organizationId: string, filters?: FilterOptions): Promise<ChartData> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `contract_pie_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            // Build where clause for User table
            const userWhere: any = {
                organization_id: organizationId
            };

            if (filters?.branchId) {
                userWhere.branch_id = filters.branchId;
            }

            const contractCounts = await UserEmploymentContract.findAll({
                include: [{
                    model: User,
                    as: 'user_employment_contract',
                    where: userWhere,
                    attributes: [] // Don't select user attributes for better performance
                }],
                attributes: [
                    'contract_status',
                    [UserEmploymentContract.sequelize!.fn('COUNT', UserEmploymentContract.sequelize!.col('UserEmploymentContract.id')), 'count']
                ],
                group: ['contract_status'],
                raw: true
            });

            const statusMap = {
                [contract_status.ACTIVE]: 'Active',
                [contract_status.INACTIVE]: 'Inactive',
                [contract_status.DELETED]: 'Terminated'
            };

            const backgroundColors = ['#10B981', '#F59E0B', '#EF4444'];

            // More efficient processing
            const validContracts = contractCounts.filter((item: any) =>
                statusMap[item.contract_status as keyof typeof statusMap]
            );

            const labels = validContracts.map((item: any) =>
                statusMap[item.contract_status as keyof typeof statusMap]
            );

            const data = validContracts.map((item: any) =>
                parseInt(item.count, 10) || 0
            );

            return {
                labels,
                datasets: [{
                    data,
                    backgroundColor: backgroundColors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            };
        } catch (error) {
            console.error('Error in getUserContractPieData:', error);
            return {
                labels: [],
                datasets: []
            };
        }
    });
};

/**
 * Get leave comparison data by branch for line chart
 * Heavily optimized with parallel queries and efficient data processing
 */
const getLeaveComparisonByBranchData = async (organizationId: string, filters?: FilterOptions): Promise<ChartData> => {
    if (!organizationId) {
        throw new Error('organizationId is required');
    }

    const cacheKey = `leave_comparison_${organizationId}_${filters?.branchId || 'all'}`;

    return getCachedData(cacheKey, async () => {
        try {
            const { start: sixMonthsAgo, monthKeys, monthLabels } = generateMonthRange(6);

            // Build where clauses
            const branchWhere: any = {
                organization_id: organizationId
            };

            const userWhere: any = {
                organization_id: organizationId
            };

            if (filters?.branchId) {
                branchWhere.id = filters.branchId;
                userWhere.branch_id = filters.branchId;
            }

            // Execute queries in parallel for better performance
            const [branches, leaveData] = await Promise.all([
                Branch.findAll({
                    where: branchWhere,
                    attributes: ['id', 'branch_name'],
                    raw: true
                }),
                UserRequest.findAll({
                    where: {
                        request_type: 'casual',
                        request_status: request_status.APPROVED
                    },
                    include: [{
                        model: User,
                        as: 'request_from_users',
                        where: userWhere,
                        attributes: ['branch_id'],
                        required: true
                    }],
                    attributes: ['leave_days', 'createdAt'],
                    raw: true
                })
            ]);

            if (branches.length === 0) {
                return { labels: monthLabels, datasets: [] };
            }

            // Create efficient lookup structures
            const branchMap = new Map(branches.map((b: any) => [b.id, b.branch_name]));
            const leaveByBranchAndMonth = new Map<string, Map<string, number>>();

            // Initialize data structure
            branches.forEach((branch: any) => {
                const monthMap = new Map<string, number>();
                monthKeys.forEach(key => monthMap.set(key, 0));
                leaveByBranchAndMonth.set(branch.id.toString(), monthMap);
            });

            // Single pass through leave data to aggregate
            leaveData.forEach((leave: any) => {
                if (leave.createdAt && leave['request_from_users.branch_id']) {
                    const leaveDate = new Date(leave.createdAt);
                    // Filter by date range in processing since database filtering didn't work
                    if (leaveDate >= sixMonthsAgo) {
                        const monthKey = leaveDate.toISOString().slice(0, 7);
                        const branchId = leave['request_from_users.branch_id'].toString();
                        const branchData = leaveByBranchAndMonth.get(branchId);

                        if (branchData && branchData.has(monthKey)) {
                            const currentTotal = branchData.get(monthKey) || 0;
                            branchData.set(monthKey, currentTotal + (leave.leave_days || 0));
                        }
                    }
                }
            });

            // Generate datasets efficiently
            const colors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

            const datasets = branches.map((branch: any, index: number) => {
                const branchData = leaveByBranchAndMonth.get(branch.id.toString());
                const monthlyData = monthKeys.map(key => branchData?.get(key) || 0);

                return {
                    label: branchMap.get(branch.id) || 'Unknown Branch',
                    data: monthlyData,
                    borderColor: colors[index % colors.length],
                    backgroundColor: colors[index % colors.length] + '20',
                    tension: 0.4
                };
            });

            return {
                labels: monthLabels,
                datasets
            };
        } catch (error) {
            console.error('Error in getLeaveComparisonByBranchData:', error);
            return {
                labels: [],
                datasets: []
            };
        }
    });
};

export interface DashboardWidget {
    id: number;
    title: string;
    type: string;
    data: any;
    order: number;
    category: "count" | "chart";
    chartType?: "line" | "bar" | "pie";
}

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users
 */
export const getUserDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: FilterOptions
): Promise<DashboardWidget[]> => {
    // Validate input parameters
    validateParameters(organizationId, userId);

    try {
        // Static count widgets (no database calls needed)
        const countWidgets: DashboardWidget[] = [
            {
                id: 1,
                title: "My Tasks",
                type: "task_summary",
                data: {
                    pending: 5,
                    completed: 12,
                    overdue: 2
                },
                order: 1,
                category: "count"
            },
            {
                id: 2,
                title: "My Leave Balance",
                type: "leave_balance",
                data: {
                    available: 15,
                    used: 10,
                    pending: 2
                },
                order: 2,
                category: "count"
            },
            {
                id: 3,
                title: "My Recent Activities",
                type: "activity_feed",
                data: {
                    activities: [
                        { action: "DSR Submitted", date: "2024-01-15", status: "completed" },
                        { action: "Leave Request", date: "2024-01-14", status: "pending" }
                    ]
                },
                order: 3,
                category: "count"
            },
            {
                id: 4,
                title: "My Performance",
                type: "performance_chart",
                data: {
                    currentMonth: 85,
                    lastMonth: 78,
                    trend: "up"
                },
                order: 4,
                category: "count"
            }
        ];

        // Early return if only count widgets are requested
        if (filters?.widgetType === "counts") {
            return countWidgets;
        }

        // Execute chart data fetching in parallel for better performance
        const chartDataPromises = [
            getOnboardingPipelineLineData(organizationId, filters),
            getOnboardingPipelineBarData(organizationId),
            getUserContractPieData(organizationId, filters),
            getLeaveComparisonByBranchData(organizationId, filters)
        ];

        // Use Promise.allSettled to handle partial failures gracefully
        const chartResults = await Promise.allSettled(chartDataPromises);

        // Create chart widgets with error handling for each
        const chartWidgets: DashboardWidget[] = [];

        // Widget 1: Onboarding Pipeline Status - Line Chart
        if (chartResults[0].status === 'fulfilled') {
            chartWidgets.push({
                id: 5,
                title: "Onboarding Pipeline Status",
                type: "line_chart",
                data: {
                    chartType: "line",
                    chartData: chartResults[0].value,
                    filters: { branchFilter: true },
                    description: "Shows onboarding pipeline status over time (completed users only)"
                },
                order: 5,
                category: "chart",
                chartType: "line"
            });
        }

        // Widget 2: Onboarding Pipeline Status - Bar Chart
        if (chartResults[1].status === 'fulfilled') {
            chartWidgets.push({
                id: 6,
                title: "Onboarding Status Overview",
                type: "bar_chart",
                data: {
                    chartType: "bar",
                    chartData: chartResults[1].value,
                    description: "Compare completed vs pending onboarding status"
                },
                order: 6,
                category: "chart",
                chartType: "bar"
            });
        }

        // Widget 3: User Contract Graph - Pie Chart
        if (chartResults[2].status === 'fulfilled') {
            chartWidgets.push({
                id: 7,
                title: "User Contract Distribution",
                type: "pie_chart",
                data: {
                    chartType: "pie",
                    chartData: chartResults[2].value,
                    filters: { branchFilter: true },
                    description: "Distribution of user contract statuses"
                },
                order: 7,
                category: "chart",
                chartType: "pie"
            });
        }

        // Widget 4: Leave Comparison Branch wise - Line Chart
        if (chartResults[3].status === 'fulfilled') {
            chartWidgets.push({
                id: 8,
                title: "Leave Comparison by Branch",
                type: "line_chart",
                data: {
                    chartType: "line",
                    chartData: chartResults[3].value,
                    filters: { branchFilter: true },
                    description: "Monthly leave data comparison across branches"
                },
                order: 8,
                category: "chart",
                chartType: "line"
            });
        }

        // Log any failed chart data fetches for monitoring
        chartResults.forEach((result, index) => {
            if (result.status === 'rejected') {
                console.error(`Chart data fetch failed for widget ${index + 5}:`, result.reason);
            }
        });

        // Combine widgets based on filter requirements
        let allWidgets: DashboardWidget[] = [];

        if (filters?.widgetType === "charts") {
            allWidgets = chartWidgets;
        } else {
            // Default: return both count and chart widgets
            allWidgets = [...countWidgets, ...chartWidgets];
        }

        // Apply chart type filtering if specified
        if (filters?.widgetType === "charts" && filters?.chartType) {
            allWidgets = allWidgets.filter(widget => widget.chartType === filters.chartType);
        }

        return allWidgets;
    } catch (error) {
        console.error('Error in getUserDashboardWidgets:', error);
        // Return at least the count widgets on error to provide partial functionality
        if (filters?.widgetType !== "charts") {
            return [
                {
                    id: 1,
                    title: "My Tasks",
                    type: "task_summary",
                    data: { pending: 0, completed: 0, overdue: 0 },
                    order: 1,
                    category: "count"
                }
            ];
        }
        throw error;
    }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
export const getDsrDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const _unused = { organizationId, userId, filters };
        const widgets: DashboardWidget[] = [];

        // DSR-specific widgets
        widgets.push({
            id: 1,
            title: "DSR Summary",
            type: "dsr_stats",
            data: {
                totalDSRs: 150,
                pendingDSRs: 25,
                approvedDSRs: 120,
                rejectedDSRs: 5
            },
            order: 1,
            category: "count"
        });

        widgets.push({
            id: 2,
            title: "Recent DSR Activities",
            type: "dsr_activities",
            data: {
                recentSubmissions: 12,
                pendingApprovals: 8,
                todaysSubmissions: 5
            },
            order: 2,
            category: "count"
        });

        widgets.push({
            id: 3,
            title: "DSR Performance",
            type: "dsr_performance",
            data: {
                onTimeSubmissions: 85,
                lateSubmissions: 10,
                averageRating: 4.2
            },
            order: 3,
            category: "count"
        });

        widgets.push({
            id: 4,
            title: "Team DSR Status",
            type: "team_dsr_status",
            data: {
                totalTeamMembers: 15,
                submittedToday: 12,
                pendingToday: 3
            },
            order: 4,
            category: "count"
        });

        return widgets;
    } catch (error) {
        console.error('Error in getAdminDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const _unused = { organizationId, userId, filters };
        const widgets: DashboardWidget[] = [];

        // Setup-specific widgets
        widgets.push({
            id: 1,
            title: "System Configuration",
            type: "system_config",
            data: {
                completedSteps: 8,
                totalSteps: 12,
                progressPercentage: 67
            },
            order: 1,
            category: "count"
        });

        widgets.push({
            id: 2,
            title: "User Setup",
            type: "user_setup",
            data: {
                totalUsers: 25,
                activeUsers: 20,
                pendingInvitations: 5
            },
            order: 2,
            category: "count"
        });

        widgets.push({
            id: 3,
            title: "Module Configuration",
            type: "module_config",
            data: {
                enabledModules: 8,
                totalModules: 12,
                pendingConfiguration: 4
            },
            order: 3,
            category: "count"
        });

        widgets.push({
            id: 4,
            title: "Integration Status",
            type: "integration_status",
            data: {
                connectedServices: 3,
                totalServices: 6,
                failedConnections: 1
            },
            order: 4,
            category: "count"
        });

        return widgets;
    } catch (error) {
        console.error('Error in getSetupDashboardWidgets:', error);
        throw error;
    }
};

export default {
    getUserDashboardWidgets,
    getDsrDashboardWidgets,
    getSetupDashboardWidgets
};
