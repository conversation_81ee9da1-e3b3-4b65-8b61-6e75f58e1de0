import { Dashboard, dashboard_status } from "../models/Dashboard";
import { DashboardModel, model_status } from "../models/DashboardModel";
import { User, user_status } from "../models/User";
import { UserEmploymentContract, contract_status } from "../models/UserEmployementContract";
import { UserRequest, request_status } from "../models/UserRequest";
import { Branch } from "../models/Branch";
import { generateDashboard } from "../helper/common";
import { Op } from "sequelize";

/**
 * Dashboard Service
 * Contains data functions for retrieving dashboard widgets
 */

/**
 * Get onboarding pipeline status data for line chart
 */
const getOnboardingPipelineLineData = async (organizationId: string, filters?: any) => {
    try {
        const branchFilter = filters?.branchId ? { branch_id: filters.branchId } : {};

        // Get completed users over the last 6 months
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const completedUsers = await User.findAll({
            where: {
                organization_id: organizationId,
                user_status: user_status.COMPLETED,
                createdAt: {
                    [Op.gte]: sixMonthsAgo
                },
                ...branchFilter
            },
            include: [{
                model: Branch,
                as: 'branch',
                attributes: ['branch_name']
            }],
            attributes: ['id', 'createdAt', 'user_status'],
            order: [['createdAt', 'ASC']]
        });

        // Group by month
        const monthlyData = [];
        for (let i = 5; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
            const monthName = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

            const count = completedUsers.filter(user =>
                user.createdAt && user.createdAt.toISOString().slice(0, 7) === monthKey
            ).length;

            monthlyData.push({
                month: monthName,
                completed: count
            });
        }

        return {
            labels: monthlyData.map(d => d.month),
            datasets: [{
                label: 'Completed Onboarding',
                data: monthlyData.map(d => d.completed),
                borderColor: '#4F46E5',
                backgroundColor: 'rgba(79, 70, 229, 0.1)',
                tension: 0.4
            }]
        };
    } catch (error) {
        console.error('Error in getOnboardingPipelineLineData:', error);
        return {
            labels: [],
            datasets: []
        };
    }
};

/**
 * Get onboarding pipeline status data for bar chart
 */
const getOnboardingPipelineBarData = async (organizationId: string) => {
    try {
        const statusCounts = await User.findAll({
            where: {
                organization_id: organizationId
            },
            attributes: [
                'user_status',
                [User.sequelize?.fn('COUNT', User.sequelize?.col('id')), 'count']
            ],
            group: ['user_status'],
            raw: true
        });

        const statusMap = {
            [user_status.COMPLETED]: 'Completed',
            [user_status.PENDING]: 'Pending',
            [user_status.ONGOING]: 'Ongoing',
            [user_status.VERIFIED]: 'Verified'
        };

        const labels = [];
        const data = [];
        const backgroundColors = ['#10B981', '#F59E0B', '#3B82F6', '#8B5CF6'];

        statusCounts.forEach((item: any, index) => {
            if (statusMap[item.user_status as keyof typeof statusMap]) {
                labels.push(statusMap[item.user_status as keyof typeof statusMap]);
                data.push(parseInt(item.count));
            }
        });

        return {
            labels,
            datasets: [{
                label: 'User Count',
                data,
                backgroundColor: backgroundColors.slice(0, labels.length),
                borderWidth: 1
            }]
        };
    } catch (error) {
        console.error('Error in getOnboardingPipelineBarData:', error);
        return {
            labels: [],
            datasets: []
        };
    }
};

/**
 * Get user contract status data for pie chart
 */
const getUserContractPieData = async (organizationId: string, filters?: any) => {
    try {
        const branchFilter = filters?.branchId ? { branch_id: filters.branchId } : {};

        const contractCounts = await UserEmploymentContract.findAll({
            include: [{
                model: User,
                as: 'user_employment_contract',
                where: {
                    organization_id: organizationId,
                    ...branchFilter
                },
                attributes: []
            }],
            attributes: [
                'contract_status',
                [UserEmploymentContract.sequelize?.fn('COUNT', UserEmploymentContract.sequelize?.col('UserEmploymentContract.id')), 'count']
            ],
            group: ['contract_status'],
            raw: true
        });

        const statusMap = {
            [contract_status.ACTIVE]: 'Active',
            [contract_status.INACTIVE]: 'Inactive',
            [contract_status.DELETED]: 'Terminated'
        };

        const labels = [];
        const data = [];
        const backgroundColors = ['#10B981', '#F59E0B', '#EF4444'];

        contractCounts.forEach((item: any) => {
            if (statusMap[item.contract_status as keyof typeof statusMap]) {
                labels.push(statusMap[item.contract_status as keyof typeof statusMap]);
                data.push(parseInt(item.count));
            }
        });

        return {
            labels,
            datasets: [{
                data,
                backgroundColor: backgroundColors.slice(0, labels.length),
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        };
    } catch (error) {
        console.error('Error in getUserContractPieData:', error);
        return {
            labels: [],
            datasets: []
        };
    }
};

/**
 * Get leave comparison data by branch for line chart
 */
const getLeaveComparisonByBranchData = async (organizationId: string, filters?: any) => {
    try {
        const branchFilter = filters?.branchId ? { id: filters.branchId } : {};

        // Get last 6 months of leave data
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

        const branches = await Branch.findAll({
            where: {
                organization_id: organizationId,
                ...branchFilter
            },
            attributes: ['id', 'branch_name']
        });

        const leaveData = await UserRequest.findAll({
            where: {
                request_type: 'casual',
                request_status: request_status.APPROVED,
                createdAt: {
                    [Op.gte]: sixMonthsAgo
                }
            },
            include: [{
                model: User,
                as: 'request_from_users',
                where: {
                    organization_id: organizationId
                },
                include: [{
                    model: Branch,
                    as: 'branch',
                    attributes: ['id', 'branch_name']
                }],
                attributes: ['id', 'branch_id']
            }],
            attributes: ['id', 'leave_days', 'createdAt'],
            order: [['createdAt', 'ASC']]
        });

        // Generate monthly labels
        const monthlyLabels = [];
        for (let i = 5; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            monthlyLabels.push(date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }));
        }

        // Process data by branch
        const datasets = branches.map((branch: any, index: number) => {
            const branchLeaveData = leaveData.filter((leave: any) =>
                leave.fromUser?.branch?.id === branch.id
            );

            const monthlyData = [];
            for (let i = 5; i >= 0; i--) {
                const date = new Date();
                date.setMonth(date.getMonth() - i);
                const monthKey = date.toISOString().slice(0, 7);

                const monthLeaves = branchLeaveData.filter((leave: any) =>
                    leave.createdAt && leave.createdAt.toISOString().slice(0, 7) === monthKey
                );

                const totalDays = monthLeaves.reduce((sum: number, leave: any) =>
                    sum + (leave.leave_days || 0), 0
                );

                monthlyData.push(totalDays);
            }

            const colors = ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

            return {
                label: branch.branch_name,
                data: monthlyData,
                borderColor: colors[index % colors.length],
                backgroundColor: colors[index % colors.length] + '20',
                tension: 0.4
            };
        });

        return {
            labels: monthlyLabels,
            datasets
        };
    } catch (error) {
        console.error('Error in getLeaveComparisonByBranchData:', error);
        return {
            labels: [],
            datasets: []
        };
    }
};

export interface DashboardWidget {
    id: number;
    title: string;
    type: string;
    data: any;
    order: number;
}

/**
 * Get user-specific dashboard widgets
 * Returns widgets relevant to regular users
 */
export const getUserDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // User-specific widgets
        widgets.push({
            id: 1,
            title: "My Tasks",
            type: "task_summary",
            data: {
                pending: 5,
                completed: 12,
                overdue: 2
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "My Leave Balance",
            type: "leave_balance",
            data: {
                available: 15,
                used: 10,
                pending: 2
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "My Recent Activities",
            type: "activity_feed",
            data: {
                activities: [
                    { action: "DSR Submitted", date: "2024-01-15", status: "completed" },
                    { action: "Leave Request", date: "2024-01-14", status: "pending" }
                ]
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "My Performance",
            type: "performance_chart",
            data: {
                currentMonth: 85,
                lastMonth: 78,
                trend: "up"
            },
            order: 4
        });

        // New Widget 1: Onboarding Pipeline Status - Line Chart
        const onboardingLineData = await getOnboardingPipelineLineData(organizationId, filters);
        widgets.push({
            id: 5,
            title: "Onboarding Pipeline Status",
            type: "line_chart",
            data: {
                chartType: "line",
                chartData: onboardingLineData,
                filters: {
                    branchFilter: true
                },
                description: "Shows onboarding pipeline status over time (completed users only)"
            },
            order: 5
        });

        // New Widget 2: Onboarding Pipeline Status - Bar Chart
        const onboardingBarData = await getOnboardingPipelineBarData(organizationId);
        widgets.push({
            id: 6,
            title: "Onboarding Status Overview",
            type: "bar_chart",
            data: {
                chartType: "bar",
                chartData: onboardingBarData,
                description: "Compare completed vs pending onboarding status"
            },
            order: 6
        });

        // New Widget 3: User Contract Graph - Pie Chart
        const contractPieData = await getUserContractPieData(organizationId, filters);
        widgets.push({
            id: 7,
            title: "User Contract Distribution",
            type: "pie_chart",
            data: {
                chartType: "pie",
                chartData: contractPieData,
                filters: {
                    branchFilter: true
                },
                description: "Distribution of user contract statuses"
            },
            order: 7
        });

        // New Widget 4: Leave Comparison Branch wise - Line Chart
        const leaveComparisonData = await getLeaveComparisonByBranchData(organizationId, filters);
        widgets.push({
            id: 8,
            title: "Leave Comparison by Branch",
            type: "line_chart",
            data: {
                chartType: "line",
                chartData: leaveComparisonData,
                filters: {
                    branchFilter: true
                },
                description: "Monthly leave data comparison across branches"
            },
            order: 8
        });

        return widgets;
    } catch (error) {
        console.error('Error in getUserDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get admin-specific dashboard widgets
 * Returns widgets relevant to administrators
 */
export const getDsrDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // DSR-specific widgets
        widgets.push({
            id: 1,
            title: "DSR Summary",
            type: "dsr_stats",
            data: {
                totalDSRs: 150,
                pendingDSRs: 25,
                approvedDSRs: 120,
                rejectedDSRs: 5
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "Recent DSR Activities",
            type: "dsr_activities",
            data: {
                recentSubmissions: 12,
                pendingApprovals: 8,
                todaysSubmissions: 5
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "DSR Performance",
            type: "dsr_performance",
            data: {
                onTimeSubmissions: 85,
                lateSubmissions: 10,
                averageRating: 4.2
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "Team DSR Status",
            type: "team_dsr_status",
            data: {
                totalTeamMembers: 15,
                submittedToday: 12,
                pendingToday: 3
            },
            order: 4
        });

        return widgets;
    } catch (error) {
        console.error('Error in getAdminDashboardWidgets:', error);
        throw error;
    }
};

/**
 * Get setup-specific dashboard widgets
 * Returns widgets relevant to system setup
 */
export const getSetupDashboardWidgets = async (
    organizationId: string,
    userId: number,
    filters?: any
): Promise<DashboardWidget[]> => {
    try {
        const widgets: DashboardWidget[] = [];

        // Setup-specific widgets
        widgets.push({
            id: 1,
            title: "System Configuration",
            type: "system_config",
            data: {
                completedSteps: 8,
                totalSteps: 12,
                progressPercentage: 67
            },
            order: 1
        });

        widgets.push({
            id: 2,
            title: "User Setup",
            type: "user_setup",
            data: {
                totalUsers: 25,
                activeUsers: 20,
                pendingInvitations: 5
            },
            order: 2
        });

        widgets.push({
            id: 3,
            title: "Module Configuration",
            type: "module_config",
            data: {
                enabledModules: 8,
                totalModules: 12,
                pendingConfiguration: 4
            },
            order: 3
        });

        widgets.push({
            id: 4,
            title: "Integration Status",
            type: "integration_status",
            data: {
                connectedServices: 3,
                totalServices: 6,
                failedConnections: 1
            },
            order: 4
        });

        return widgets;
    } catch (error) {
        console.error('Error in getSetupDashboardWidgets:', error);
        throw error;
    }
};

export default {
    getUserDashboardWidgets,
    getDsrDashboardWidgets,
    getSetupDashboardWidgets
};
