DASHBOARD API DATA STRUCTURE & RESPONSE GUIDE
==============================================

This document shows the exact API endpoints, request/response formats, and data structures 
for the dashboard service with Recharts integration and Moment.js date handling.

==============================================
1. MAIN DASHBOARD API ENDPOINT
==============================================

ENDPOINT: GET /api/dashboard/widgets
METHOD: GET
HEADERS: 
  Content-Type: application/json
  Authorization: Bearer <your-jwt-token>

QUERY PARAMETERS:
- organizationId (required): string - Organization identifier
- userId (required): number - User identifier  
- branchId (optional): number - Filter by specific branch
- widgetType (optional): "counts" | "charts" | "all" - Filter widget types
- chartType (optional): "line" | "bar" | "pie" - Filter chart types (when widgetType="charts")

SAMPLE API CALLS:
================

1. Get All Widgets:
GET /api/dashboard/widgets?organizationId=org-123&userId=456

2. Get Only Count Widgets:
GET /api/dashboard/widgets?organizationId=org-123&userId=456&widgetType=counts

3. Get Only Chart Widgets:
GET /api/dashboard/widgets?organizationId=org-123&userId=456&widgetType=charts

4. Get Only Line Charts:
GET /api/dashboard/widgets?organizationId=org-123&userId=456&widgetType=charts&chartType=line

5. Filter by Branch:
GET /api/dashboard/widgets?organizationId=org-123&userId=456&branchId=789

==============================================
2. COMPLETE API RESPONSE STRUCTURE
==============================================

RESPONSE FORMAT:
HTTP Status: 200 OK
Content-Type: application/json

RESPONSE BODY:
[
  // COUNT WIDGETS (4 widgets)
  {
    "id": 1,
    "title": "My Tasks",
    "type": "task_summary",
    "data": {
      "pending": 5,
      "completed": 12,
      "overdue": 2
    },
    "order": 1,
    "category": "count"
  },
  {
    "id": 2,
    "title": "My Leave Balance", 
    "type": "leave_balance",
    "data": {
      "available": 15,
      "used": 10,
      "pending": 2
    },
    "order": 2,
    "category": "count"
  },
  {
    "id": 3,
    "title": "My Recent Activities",
    "type": "activity_feed", 
    "data": {
      "activities": [
        {
          "action": "DSR Submitted",
          "date": "2025-07-06",
          "status": "completed"
        },
        {
          "action": "Leave Request", 
          "date": "2025-07-05",
          "status": "pending"
        }
      ]
    },
    "order": 3,
    "category": "count"
  },
  {
    "id": 4,
    "title": "My Performance",
    "type": "performance_chart",
    "data": {
      "currentMonth": 85,
      "lastMonth": 78, 
      "trend": "up"
    },
    "order": 4,
    "category": "count"
  },

  // CHART WIDGETS (4 widgets with Recharts format)
  {
    "id": 5,
    "title": "Onboarding Pipeline Status",
    "type": "line_chart",
    "data": {
      "chartType": "line",
      "chartData": {
        "success": true,
        "chartType": "line",
        "title": "Onboarding Pipeline Status",
        "data": {
          "data": [
            {
              "name": "Feb 2025",
              "value": 12,
              "completed": 12,
              "month": "Feb 2025"
            },
            {
              "name": "Mar 2025", 
              "value": 18,
              "completed": 18,
              "month": "Mar 2025"
            },
            {
              "name": "Apr 2025",
              "value": 25,
              "completed": 25, 
              "month": "Apr 2025"
            },
            {
              "name": "May 2025",
              "value": 30,
              "completed": 30,
              "month": "May 2025"
            },
            {
              "name": "Jun 2025",
              "value": 22,
              "completed": 22,
              "month": "Jun 2025"
            },
            {
              "name": "Jul 2025",
              "value": 35,
              "completed": 35,
              "month": "Jul 2025"
            }
          ],
          "xAxisKey": "name",
          "yAxisKey": "value", 
          "series": [
            {
              "dataKey": "completed",
              "name": "Completed Onboarding",
              "color": "#4F46E5",
              "type": "line"
            }
          ]
        },
        "metadata": {
          "totalRecords": 142,
          "dateRange": "Feb 2025 - Jul 2025",
          "filters": {
            "branchId": 789
          },
          "lastUpdated": "2025-07-07T10:30:00.000Z"
        }
      },
      "filters": {
        "branchFilter": true
      },
      "description": "Shows onboarding pipeline status over time (completed users only)"
    },
    "order": 5,
    "category": "chart",
    "chartType": "line"
  },

  {
    "id": 6,
    "title": "Onboarding Status Overview",
    "type": "bar_chart",
    "data": {
      "chartType": "bar",
      "chartData": {
        "success": true,
        "chartType": "bar", 
        "title": "Onboarding Status Overview",
        "data": {
          "data": [
            {
              "name": "Completed",
              "value": 120,
              "count": 120,
              "status": "Completed",
              "color": "#10B981"
            },
            {
              "name": "Pending",
              "value": 25,
              "count": 25,
              "status": "Pending", 
              "color": "#F59E0B"
            },
            {
              "name": "Ongoing",
              "value": 15,
              "count": 15,
              "status": "Ongoing",
              "color": "#3B82F6"
            },
            {
              "name": "Verified",
              "value": 8,
              "count": 8,
              "status": "Verified",
              "color": "#8B5CF6"
            }
          ],
          "xAxisKey": "name",
          "yAxisKey": "value",
          "series": [
            {
              "dataKey": "count",
              "name": "User Count",
              "color": "#4F46E5",
              "type": "bar"
            }
          ]
        },
        "metadata": {
          "totalRecords": 168,
          "lastUpdated": "2025-07-07T10:30:00.000Z"
        }
      },
      "description": "Compare completed vs pending onboarding status"
    },
    "order": 6,
    "category": "chart",
    "chartType": "bar"
  },

  {
    "id": 7,
    "title": "User Contract Distribution",
    "type": "pie_chart",
    "data": {
      "chartType": "pie",
      "chartData": {
        "success": true,
        "chartType": "pie",
        "title": "User Contract Distribution", 
        "data": {
          "data": [
            {
              "name": "Active",
              "value": 85,
              "status": "Active",
              "count": 85,
              "color": "#10B981"
            },
            {
              "name": "Inactive", 
              "value": 12,
              "status": "Inactive",
              "count": 12,
              "color": "#F59E0B"
            },
            {
              "name": "Terminated",
              "value": 3,
              "status": "Terminated",
              "count": 3,
              "color": "#EF4444"
            }
          ],
          "nameKey": "name",
          "valueKey": "value",
          "colors": ["#10B981", "#F59E0B", "#EF4444"]
        },
        "metadata": {
          "totalRecords": 100,
          "filters": {
            "branchId": 789
          },
          "lastUpdated": "2025-07-07T10:30:00.000Z"
        }
      },
      "filters": {
        "branchFilter": true
      },
      "description": "Distribution of user contract statuses"
    },
    "order": 7,
    "category": "chart",
    "chartType": "pie"
  },

  {
    "id": 8,
    "title": "Leave Comparison by Branch",
    "type": "line_chart",
    "data": {
      "chartType": "line",
      "chartData": {
        "success": true,
        "chartType": "line",
        "title": "Leave Comparison by Branch",
        "data": {
          "data": [
            {
              "name": "Feb 2025",
              "value": 45,
              "month": "Feb 2025",
              "Engineering": 20,
              "Marketing": 15,
              "Sales": 10
            },
            {
              "name": "Mar 2025",
              "value": 52,
              "month": "Mar 2025", 
              "Engineering": 25,
              "Marketing": 17,
              "Sales": 10
            },
            {
              "name": "Apr 2025",
              "value": 38,
              "month": "Apr 2025",
              "Engineering": 18,
              "Marketing": 12,
              "Sales": 8
            },
            {
              "name": "May 2025",
              "value": 60,
              "month": "May 2025",
              "Engineering": 30,
              "Marketing": 20,
              "Sales": 10
            },
            {
              "name": "Jun 2025", 
              "value": 42,
              "month": "Jun 2025",
              "Engineering": 22,
              "Marketing": 12,
              "Sales": 8
            },
            {
              "name": "Jul 2025",
              "value": 55,
              "month": "Jul 2025",
              "Engineering": 28,
              "Marketing": 18,
              "Sales": 9
            }
          ],
          "xAxisKey": "name",
          "yAxisKey": "value",
          "series": [
            {
              "dataKey": "Engineering",
              "name": "Engineering",
              "color": "#4F46E5",
              "type": "line"
            },
            {
              "dataKey": "Marketing", 
              "name": "Marketing",
              "color": "#10B981",
              "type": "line"
            },
            {
              "dataKey": "Sales",
              "name": "Sales",
              "color": "#F59E0B",
              "type": "line"
            }
          ]
        },
        "metadata": {
          "totalRecords": 292,
          "dateRange": "Feb 2025 - Jul 2025",
          "filters": {
            "branchId": 789
          },
          "lastUpdated": "2025-07-07T10:30:00.000Z"
        }
      },
      "filters": {
        "branchFilter": true
      },
      "description": "Monthly leave data comparison across branches"
    },
    "order": 8,
    "category": "chart", 
    "chartType": "line"
  }
]

==============================================
3. ERROR RESPONSE FORMAT
==============================================

HTTP Status: 400/500
Content-Type: application/json

ERROR RESPONSE:
{
  "error": {
    "message": "Invalid organizationId: must be a non-empty string",
    "code": "VALIDATION_ERROR",
    "timestamp": "2025-07-07T10:30:00.000Z"
  }
}

PARTIAL SUCCESS (Some charts failed):
HTTP Status: 200 OK
- Count widgets will always be returned
- Failed chart widgets will be omitted
- Check chartData.success field for individual chart status

==============================================
4. FRONTEND INTEGRATION EXAMPLES
==============================================

JAVASCRIPT/REACT USAGE:

// Fetch dashboard data
const fetchDashboardData = async () => {
  try {
    const response = await fetch(
      '/api/dashboard/widgets?organizationId=org-123&userId=456&widgetType=charts',
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch dashboard data');
    }
    
    const widgets = await response.json();
    return widgets;
  } catch (error) {
    console.error('Dashboard API Error:', error);
    return [];
  }
};

// Use with Recharts
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';

const DashboardChart = ({ widget }) => {
  const chartData = widget.data.chartData;
  
  if (!chartData.success) {
    return <div>Failed to load chart data</div>;
  }
  
  switch (chartData.chartType) {
    case 'line':
      return (
        <LineChart data={chartData.data.data} width={600} height={300}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey={chartData.data.xAxisKey} />
          <YAxis />
          <Tooltip />
          <Legend />
          {chartData.data.series.map((series, index) => (
            <Line 
              key={index}
              type="monotone" 
              dataKey={series.dataKey} 
              stroke={series.color} 
              name={series.name}
            />
          ))}
        </LineChart>
      );
      
    case 'bar':
      return (
        <BarChart data={chartData.data.data} width={600} height={300}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey={chartData.data.xAxisKey} />
          <YAxis />
          <Tooltip />
          <Bar dataKey="count" fill="#4F46E5" />
        </BarChart>
      );
      
    case 'pie':
      return (
        <PieChart width={400} height={400}>
          <Pie
            data={chartData.data.data}
            dataKey={chartData.data.valueKey}
            nameKey={chartData.data.nameKey}
            cx="50%"
            cy="50%"
            outerRadius={80}
          >
            {chartData.data.data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      );
  }
};

==============================================
5. KEY FEATURES & BENEFITS
==============================================

✅ RECHARTS READY: Direct integration with Recharts library
✅ MOMENT.JS DATES: Consistent date handling with validation
✅ ERROR HANDLING: Graceful degradation and detailed error info
✅ CACHING: 5-minute cache for improved performance
✅ FILTERING: Support for branch, widget type, and chart type filters
✅ METADATA: Rich metadata for enhanced UX
✅ TYPE SAFETY: Full TypeScript support
✅ PERFORMANCE: Parallel data fetching and optimized queries

==============================================
6. TROUBLESHOOTING
==============================================

COMMON ISSUES:

1. Empty chart data:
   - Check if organizationId exists in database
   - Verify user has access to the organization
   - Check date ranges (data might be outside 6-month window)

2. Authentication errors:
   - Ensure valid JWT token in Authorization header
   - Check token expiration

3. Performance issues:
   - Use widgetType filter to get only needed widgets
   - Implement frontend caching for repeated requests
   - Consider pagination for large datasets

4. Chart rendering issues:
   - Always check chartData.success before rendering
   - Handle empty data arrays gracefully
   - Verify Recharts version compatibility

==============================================
7. CONTROLLER IMPLEMENTATION EXAMPLE
==============================================

// controllers/dashboard.controller.ts
import { Request, Response } from 'express';
import { getUserDashboardWidgets } from '../services/dashboard.service';

export const getDashboardWidgets = async (req: Request, res: Response) => {
  try {
    const { organizationId, userId } = req.query;
    const filters = {
      branchId: req.query.branchId ? parseInt(req.query.branchId as string) : undefined,
      widgetType: req.query.widgetType as "counts" | "charts" | "all" | undefined,
      chartType: req.query.chartType as "line" | "bar" | "pie" | undefined
    };

    // Validate required parameters
    if (!organizationId || !userId) {
      return res.status(400).json({
        error: {
          message: 'organizationId and userId are required',
          code: 'MISSING_PARAMETERS',
          timestamp: new Date().toISOString()
        }
      });
    }

    const widgets = await getUserDashboardWidgets(
      organizationId as string,
      parseInt(userId as string),
      filters
    );

    res.status(200).json(widgets);
  } catch (error) {
    console.error('Dashboard Controller Error:', error);
    res.status(500).json({
      error: {
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      }
    });
  }
};

// routes/dashboard.routes.ts
import { Router } from 'express';
import { getDashboardWidgets } from '../controllers/dashboard.controller';
import { authenticateToken } from '../middleware/auth';

const router = Router();

router.get('/widgets', authenticateToken, getDashboardWidgets);

export default router;

==============================================
8. DATABASE QUERIES BEING EXECUTED
==============================================

The service executes these optimized database queries:

1. ONBOARDING LINE CHART:
SELECT id, createdAt
FROM nv_users
WHERE organization_id = ?
  AND user_status = 'COMPLETED'
  AND createdAt >= ?
  AND branch_id = ? (if filtered)
ORDER BY createdAt ASC;

2. ONBOARDING BAR CHART:
SELECT user_status, COUNT(id) as count
FROM nv_users
WHERE organization_id = ?
GROUP BY user_status;

3. CONTRACT PIE CHART:
SELECT contract_status, COUNT(UserEmploymentContract.id) as count
FROM nv_user_employment_contracts
INNER JOIN nv_users ON nv_users.id = nv_user_employment_contracts.user_id
WHERE nv_users.organization_id = ?
  AND nv_users.branch_id = ? (if filtered)
GROUP BY contract_status;

4. LEAVE COMPARISON CHART:
SELECT leave_days, createdAt
FROM nv_request
INNER JOIN nv_users ON nv_users.id = nv_request.from_user_id
WHERE nv_request.request_type = 'casual'
  AND nv_request.request_status = 'APPROVED'
  AND nv_users.organization_id = ?
  AND nv_users.branch_id = ? (if filtered);

5. BRANCH DATA:
SELECT id, branch_name
FROM nv_branches
WHERE organization_id = ?
  AND id = ? (if filtered);

==============================================

This API provides production-ready dashboard data with Recharts integration.
All responses are optimized for performance and include comprehensive error handling.

READY FOR FRONTEND INTEGRATION! 🚀
