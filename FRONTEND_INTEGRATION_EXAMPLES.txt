FRONTEND INTEGRATION EXAMPLES - DASHBOARD API
============================================

This file provides practical examples for integrating the dashboard API 
with your frontend application using Recharts library.

============================================
1. BASIC API INTEGRATION
============================================

// utils/dashboardApi.js
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

export class DashboardAPI {
  constructor(token) {
    this.token = token;
    this.baseHeaders = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  async getDashboardWidgets(organizationId, userId, filters = {}) {
    const params = new URLSearchParams({
      organizationId,
      userId: userId.toString(),
      ...filters
    });

    try {
      const response = await fetch(`${API_BASE_URL}/dashboard/widgets?${params}`, {
        method: 'GET',
        headers: this.baseHeaders
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error?.message || 'Failed to fetch dashboard data');
      }

      return await response.json();
    } catch (error) {
      console.error('Dashboard API Error:', error);
      throw error;
    }
  }

  // Specific methods for different widget types
  async getCountWidgets(organizationId, userId) {
    return this.getDashboardWidgets(organizationId, userId, { widgetType: 'counts' });
  }

  async getChartWidgets(organizationId, userId, branchId = null) {
    const filters = { widgetType: 'charts' };
    if (branchId) filters.branchId = branchId;
    return this.getDashboardWidgets(organizationId, userId, filters);
  }

  async getLineCharts(organizationId, userId, branchId = null) {
    const filters = { widgetType: 'charts', chartType: 'line' };
    if (branchId) filters.branchId = branchId;
    return this.getDashboardWidgets(organizationId, userId, filters);
  }
}

============================================
2. REACT HOOKS FOR DASHBOARD DATA
============================================

// hooks/useDashboard.js
import { useState, useEffect, useCallback } from 'react';
import { DashboardAPI } from '../utils/dashboardApi';

export const useDashboard = (organizationId, userId, filters = {}) => {
  const [widgets, setWidgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const dashboardAPI = new DashboardAPI(localStorage.getItem('authToken'));

  const fetchDashboard = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await dashboardAPI.getDashboardWidgets(organizationId, userId, filters);
      setWidgets(data);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err.message);
      console.error('Dashboard fetch error:', err);
    } finally {
      setLoading(false);
    }
  }, [organizationId, userId, JSON.stringify(filters)]);

  useEffect(() => {
    if (organizationId && userId) {
      fetchDashboard();
    }
  }, [fetchDashboard]);

  const refresh = () => {
    fetchDashboard();
  };

  return {
    widgets,
    loading,
    error,
    lastUpdated,
    refresh
  };
};

// hooks/useChartData.js
export const useChartData = (widget) => {
  const [chartConfig, setChartConfig] = useState(null);
  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    if (!widget?.data?.chartData) {
      setIsValid(false);
      return;
    }

    const { chartData } = widget.data;
    
    if (!chartData.success) {
      setIsValid(false);
      return;
    }

    setChartConfig({
      type: chartData.chartType,
      data: chartData.data.data,
      xAxisKey: chartData.data.xAxisKey,
      yAxisKey: chartData.data.yAxisKey,
      series: chartData.data.series,
      colors: chartData.data.colors,
      metadata: chartData.metadata
    });
    setIsValid(true);
  }, [widget]);

  return { chartConfig, isValid };
};

============================================
3. RECHARTS COMPONENTS
============================================

// components/charts/LineChartWidget.jsx
import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useChartData } from '../../hooks/useChartData';

export const LineChartWidget = ({ widget }) => {
  const { chartConfig, isValid } = useChartData(widget);

  if (!isValid) {
    return (
      <div className="chart-error">
        <p>Unable to load chart data</p>
        <small>Last updated: {widget.data?.chartData?.metadata?.lastUpdated}</small>
      </div>
    );
  }

  return (
    <div className="chart-container">
      <h3>{widget.title}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={chartConfig.data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey={chartConfig.xAxisKey} 
            tick={{ fontSize: 12 }}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: '#f8f9fa', 
              border: '1px solid #dee2e6',
              borderRadius: '4px'
            }}
          />
          <Legend />
          {chartConfig.series.map((series, index) => (
            <Line
              key={index}
              type="monotone"
              dataKey={series.dataKey}
              stroke={series.color}
              name={series.name}
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 6 }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
      <div className="chart-metadata">
        <small>
          Total Records: {chartConfig.metadata?.totalRecords} | 
          Range: {chartConfig.metadata?.dateRange} |
          Updated: {new Date(chartConfig.metadata?.lastUpdated).toLocaleString()}
        </small>
      </div>
    </div>
  );
};

// components/charts/BarChartWidget.jsx
import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useChartData } from '../../hooks/useChartData';

export const BarChartWidget = ({ widget }) => {
  const { chartConfig, isValid } = useChartData(widget);

  if (!isValid) {
    return <div className="chart-error">Unable to load chart data</div>;
  }

  return (
    <div className="chart-container">
      <h3>{widget.title}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartConfig.data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey={chartConfig.xAxisKey} />
          <YAxis />
          <Tooltip />
          <Bar 
            dataKey="count" 
            fill="#4F46E5"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// components/charts/PieChartWidget.jsx
import React from 'react';
import { PieChart, Pie, Cell, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useChartData } from '../../hooks/useChartData';

export const PieChartWidget = ({ widget }) => {
  const { chartConfig, isValid } = useChartData(widget);

  if (!isValid) {
    return <div className="chart-error">Unable to load chart data</div>;
  }

  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="chart-container">
      <h3>{widget.title}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartConfig.data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomLabel}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartConfig.data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value, name) => [value, name]} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

============================================
4. MAIN DASHBOARD COMPONENT
============================================

// components/Dashboard.jsx
import React, { useState } from 'react';
import { useDashboard } from '../hooks/useDashboard';
import { LineChartWidget } from './charts/LineChartWidget';
import { BarChartWidget } from './charts/BarChartWidget';
import { PieChartWidget } from './charts/PieChartWidget';
import { CountWidget } from './CountWidget';

export const Dashboard = ({ organizationId, userId }) => {
  const [filters, setFilters] = useState({});
  const { widgets, loading, error, refresh } = useDashboard(organizationId, userId, filters);

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const renderWidget = (widget) => {
    switch (widget.type) {
      case 'line_chart':
        return <LineChartWidget key={widget.id} widget={widget} />;
      case 'bar_chart':
        return <BarChartWidget key={widget.id} widget={widget} />;
      case 'pie_chart':
        return <PieChartWidget key={widget.id} widget={widget} />;
      default:
        return <CountWidget key={widget.id} widget={widget} />;
    }
  };

  if (loading) {
    return (
      <div className="dashboard-loading">
        <div className="spinner">Loading dashboard...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-error">
        <h3>Error loading dashboard</h3>
        <p>{error}</p>
        <button onClick={refresh}>Retry</button>
      </div>
    );
  }

  const countWidgets = widgets.filter(w => w.category === 'count');
  const chartWidgets = widgets.filter(w => w.category === 'chart');

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Dashboard</h1>
        <div className="dashboard-controls">
          <select onChange={(e) => handleFilterChange({ widgetType: e.target.value })}>
            <option value="">All Widgets</option>
            <option value="counts">Count Widgets</option>
            <option value="charts">Chart Widgets</option>
          </select>
          <button onClick={refresh}>Refresh</button>
        </div>
      </div>

      {countWidgets.length > 0 && (
        <div className="count-widgets-section">
          <h2>Summary</h2>
          <div className="count-widgets-grid">
            {countWidgets.map(renderWidget)}
          </div>
        </div>
      )}

      {chartWidgets.length > 0 && (
        <div className="chart-widgets-section">
          <h2>Analytics</h2>
          <div className="chart-widgets-grid">
            {chartWidgets.map(renderWidget)}
          </div>
        </div>
      )}
    </div>
  );
};

============================================
5. CSS STYLES
============================================

/* styles/dashboard.css */
.dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.dashboard-controls {
  display: flex;
  gap: 10px;
}

.count-widgets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.chart-widgets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.chart-metadata {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
  color: #666;
  font-size: 12px;
}

.chart-error {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  color: #c53030;
}

.dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.spinner {
  font-size: 18px;
  color: #666;
}

============================================
6. TESTING THE INTEGRATION
============================================

// Test API endpoints with curl:

# Get all widgets
curl -X GET "http://localhost:3000/api/dashboard/widgets?organizationId=org-123&userId=456" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# Get only chart widgets
curl -X GET "http://localhost:3000/api/dashboard/widgets?organizationId=org-123&userId=456&widgetType=charts" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

# Get line charts for specific branch
curl -X GET "http://localhost:3000/api/dashboard/widgets?organizationId=org-123&userId=456&widgetType=charts&chartType=line&branchId=789" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"

============================================

This integration provides a complete dashboard solution with:
✅ Recharts-ready data format
✅ Error handling and loading states  
✅ Responsive design
✅ Real-time data refresh
✅ Filtering capabilities
✅ Performance optimizations

Ready for production use! 🚀
